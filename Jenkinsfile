pipeline {
  agent any

  environment {
    // Docker registry configuration
    DOCKER_REGISTRY = '**************:5000'
    DOCKER_IMAGE = 'cmc/ts/cagent/flowise'
    DOCKER_REPOSITORY = "${DOCKER_REGISTRY}/${DOCKER_IMAGE}"
    
    // Deployment webhook
    DEPLOY_WEBHOOK = 'http://**************:3000/api/compose/deploy/8aa41c22081adf175423a1ed8acfdc0cf4e7fd5e7edbd946'
  }

  stages {
    stage('Prepare Build') {
      steps {
        // Get commit hash for tagging
        script {
          def tag = sh(
            script: "git rev-parse --short HEAD",
            returnStdout: true
          ).trim()
          // Ensure tag is valid for Docker (replace '/' with '-')
          env.COMMIT_TAG = tag.replaceAll('/', '-')
          echo "✅ Using COMMIT_TAG=${env.COMMIT_TAG}"
        }
      }
    }
    
    stage('Build & Push Image') {
      steps {
        sh '''
          # Build với tag cụ thể
          docker build -t $DOCKER_REPOSITORY:$COMMIT_TAG .
          
          # Thêm tag latest
          docker tag $DOCKER_REPOSITORY:$COMMIT_TAG $DOCKER_REPOSITORY:latest
          
          # Push cả hai tag
          docker push $DOCKER_REPOSITORY:$COMMIT_TAG
          docker push $DOCKER_REPOSITORY:latest
          
          echo "✅ Pushed image: $DOCKER_REPOSITORY:$COMMIT_TAG and $DOCKER_REPOSITORY:latest"
        '''
      }
    }
    
    stage('Trigger Deployment') {
      steps {
        script {
          try {
            // Sử dụng curl để gọi webhook deployment
            def response = sh(
              script: """
                echo "🚀 Triggering deployment via webhook...."
                curl -s -o deployment_response.txt -w "%{http_code}" -X POST "${DEPLOY_WEBHOOK}" \
                  -H "Content-Type: application/json" \
                  -d '{"imageTag":"${env.COMMIT_TAG}", "repository":"${DOCKER_REPOSITORY}"}'
              """,
              returnStdout: true
            ).trim()
            
            // Kiểm tra response code
            if (response == "200" || response == "201" || response == "202") {
              echo "✅ Deployment triggered successfully with response code ${response}"
              sh "cat deployment_response.txt || echo 'No response body'"
            } else {
              echo "⚠️ Deployment triggered with unexpected response code: ${response}"
              sh "cat deployment_response.txt || echo 'No response body'"
              // Không fail pipeline nếu webhook không thành công
            }
          } catch (Exception e) {
            echo "⚠️ Warning: Failed to trigger deployment webhook: ${e.message}"
            // Không fail pipeline nếu webhook không thành công
          }
        }
      }
    }

    stage('Cleanup Images') {
      steps {
        sh '''
          echo "🧹 Cleaning up old Docker images..."
          
          # Liệt kê các image trước khi xóa
          echo "Images trước khi xóa:"
          docker images "$DOCKER_REPOSITORY" --format "{{.Repository}}:{{.Tag}}"
          
          # Lưu danh sách image cần xóa (tất cả image của dự án ngoại trừ tag latest)
          IMAGES_TO_REMOVE=$(docker images "$DOCKER_REPOSITORY" --format "{{.Repository}}:{{.Tag}}" | grep -v "$DOCKER_REPOSITORY:latest")
          
          if [ -n "$IMAGES_TO_REMOVE" ]; then
            echo "Các image sẽ bị xóa:"
            echo "$IMAGES_TO_REMOVE"
            
            # Xóa các image đã liệt kê
            echo "$IMAGES_TO_REMOVE" | xargs -r docker rmi || true
            
            echo "✅ Đã xóa các image cũ"
          else
            echo "Không có image nào cần xóa"
          fi
          
          # Liệt kê các image sau khi xóa
          echo "Images sau khi xóa:"
          docker images "$DOCKER_REPOSITORY" --format "{{.Repository}}:{{.Tag}}"
        '''
      }
    }
  }

  post {
    success {
      echo "✅ Pipeline completed successfully: Image pushed and deployment triggered for $DOCKER_REPOSITORY:$COMMIT_TAG"
    }
    failure {
      echo "❌ Pipeline thất bại!"
    }
    always {
      // Dọn dẹp file tạm nếu có
      sh 'rm -f deployment_response.txt || true'
    }
  }
}