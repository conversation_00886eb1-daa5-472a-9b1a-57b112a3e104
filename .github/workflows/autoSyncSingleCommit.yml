name: autoSyncSingleCommit
on:
    push:
        branches:
            - main
jobs:
    doNotAutoSyncSingleCommit:
        if: github.event.commits[1] != null
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v3
            - name: IGNORE autoSyncSingleCommit
              run: |
                  echo This single commit has came from a merged commit. We will ignore it. This case is handled in autoSyncMergedPullRequest workflow for merge commits comming from merged pull requests only! Beware, the regular merge commits are not handled by any workflow for the moment.
    autoSyncSingleCommit:
        if: github.event.commits[1] == null
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v3
            - name: autoSyncSingleCommit
              env:
                  GITHUB_CONTEXT: ${{ toJSON(github) }}
              run: |
                  echo Autosync a single commit with id: ${{ github.sha }} from openSource main branch towards cloud hosted version.
            - name: Repository Dispatch
              uses: peter-evans/repository-dispatch@v3
              with:
                  token: ${{ secrets.AUTOSYNC_TOKEN }}
                  repository: ${{ secrets.AUTOSYNC_CH_URL }}
                  event-type: ${{ secrets.AUTOSYNC_SC_EVENT_TYPE }}
                  client-payload: >-
                      {
                        "ref": "${{ github.ref }}",
                        "sha": "${{ github.sha }}",
                        "commitMessage": "${{ github.event.commits[0].id }}"
                      }
