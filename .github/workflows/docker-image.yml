name: Docker Image CI

on:
    workflow_dispatch:
        inputs:
            node_version:
                description: 'Node.js version to build this image with.'
                type: choice
                required: true
                default: '20'
                options:
                    - '20'
            tag_version:
                description: 'Tag version of the image to be pushed.'
                type: string
                required: true
                default: 'latest'

jobs:
    docker:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v4.1.1
            - name: Set up QEMU
              uses: docker/setup-qemu-action@v3.0.0
            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v3.0.0
            - name: Login to Docker Hub
              uses: docker/login-action@v3
              with:
                  username: ${{ secrets.DOCKERHUB_USERNAME }}
                  password: ${{ secrets.DOCKERHUB_TOKEN }}
            - name: Build and push
              uses: docker/build-push-action@v5.3.0
              with:
                  context: .
                  file: ./docker/Dockerfile
                  build-args: |
                      NODE_VERSION=${{github.event.inputs.node_version}}
                  platforms: linux/amd64,linux/arm64
                  push: true
                  tags: flowiseai/flowise:${{github.event.inputs.tag_version}}
