import express, { Router } from 'express'
import { deleteObjectController, getObjectController, listObjectsController, uploadObjectController } from '../../controllers/s3'
const router: Router = express.Router()

// Route to list objects in S3
router.get('/listS3Objects', listObjectsController)

// Route to upload an object to S3
router.post('/uploadS3Object', uploadObjectController)

// Route to get an object from S3
router.get('/getS3Object/:key', getObjectController)

// Route to delete an object from S3
router.delete('/deleteS3Object/:key', deleteObjectController)

export default router
