import express, { Router } from 'express'
import getKnowledgeBaseController from '../../controllers/get-knowledge-base'
const router: Router = express.Router()

// READ
router.post('/', getKnowledgeBaseController.getFolderNameByKB)
router.get('/', getKnowledgeBaseController.getAllKnowledgeBases)
router.get('/detail/:id', getKnowledgeBaseController.getKnowledgeBaseById)
router.get('/knowledge-base-id/:knowledgeBaseID', getKnowledgeBaseController.getKnowledgeBaseByBaseId)
router.get('/knowledge-base-name/:folderName', getKnowledgeBaseController.getKnowledgeBaseByFolderName)

export default router
