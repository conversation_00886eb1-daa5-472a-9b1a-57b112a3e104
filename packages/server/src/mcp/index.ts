import { GetCurrentTimeMCPServer } from './server/get-current-time'
import { MCPServer } from './types'
import { ReadFileMCPServer, WriteFileMCPServer } from './server/read-write-s3-file'
import { KnowledgeBaseRetrieverMCPServer } from './server/knowledge-base-retriever'
import { GetCrawlDataMCPServer } from './server/get-crawl-data'

export function registerMCPServers(addMcpServer: (mcpServer: MCPServer<any>) => void) {
  addMcpServer(GetCurrentTimeMCPServer)
  addMcpServer(ReadFileMCPServer)
  addMcpServer(WriteFileMCPServer)
  addMcpServer(KnowledgeBaseRetrieverMCPServer)
  addMcpServer(GetCrawlDataMCPServer)
}
