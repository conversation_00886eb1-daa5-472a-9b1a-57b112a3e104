﻿### Tài liệu Triển khai MCP Server Tùy chỉnh

**M<PERSON><PERSON> đích:** Hướng dẫn quy trình tạo và tích hợp một Model Context Protocol (MCP) server tùy chỉnh vào hệ thống.

**1. <PERSON><PERSON><PERSON> nghĩa Kiểu <PERSON> liệu (<PERSON><PERSON><PERSON>)**

- Nếu logic yêu cầu các cấu trúc dữ liệu phức tạp hoặc cần tái sử dụng, các kiểu TypeScript có thể được định nghĩa trong file `packages/server/src/mcp/types.ts` hoặc một file type riêng biệt.
- File `types.ts` hiện tại định nghĩa interface `MCPServer`, là cấu trúc cơ bản cho mọi MCP server.

**2. Tạo File Implementation MCP Server**

- Tạo một file TypeScript mới trong thư mục `packages/server/src/mcp/server/`. Tên file nên phản ánh chức năng của server (ví dụ: `process-user-data.ts`).

**3. Triển khai Logic MCP Server**

Nội dung file implementation cần bao gồm các thành phần sau:

- **Import Dependencies:**

  - Import interface `MCPServer` từ `../types`.
  - Import `z` từ thư viện `zod` để định nghĩa và xác thực schema cho tham số đầu vào.
  - Import các thư viện cần thiết khác cho logic xử lý.

- **Định nghĩa Object MCPServer:**

  - Khai báo một constant export tuân thủ interface `MCPServer<Args>`, trong đó `Args` là kiểu Zod schema của các tham số.
  - Cấu trúc của object bao gồm:
    - `name`: (Type: `string`) Định danh duy nhất cho MCP server. Nên sử dụng format kebab-case (ví dụ: `'process-user-data'`).
    - `description`: (Type: `string`) Mô tả ngắn gọn về chức năng của server.
    - `params`: (Type: `ZodRawShape`) Một object định nghĩa schema của các tham số đầu vào sử dụng `zod`. Mỗi key là tên tham số và value là một `zod` schema (ví dụ: `z.string()`, `z.number().optional()`). Điều này đảm bảo việc xác thực kiểu dữ liệu và giá trị mặc định (nếu có) cho các tham số.
    - `cb`: (Type: `async function`) Hàm callback bất đồng bộ chứa logic xử lý chính.
      - Nhận một object chứa các tham số đã được xác thực (theo schema định nghĩa trong `params`) làm đối số.
      - Thực thi logic nghiệp vụ dựa trên các tham số đầu vào.
      - Trả về một `Promise` chứa kết quả xử lý. Kết quả thường có cấu trúc `{ content: [{ type: 'text', text: '...' }, ...] }` để hiển thị thông tin cho client.

- **Ví dụ cấu trúc cơ bản:**

TypeScript

```typescript
import { MCPServer } from '../types'
import { z, ZodRawShape } from 'zod'
// Import các thư viện khác nếu cần

// Định nghĩa kiểu cho params nếu cần độ phức tạp
// type CustomParamsType = { userId: string, details?: object };

// Định nghĩa Zod schema cho params
const CustomParamsSchema = {
  userId: z.string().uuid(),
  limit: z.number().int().positive().optional().default(10)
  // Thêm các tham số khác tại đây
}

export const CustomMCPServer: MCPServer<typeof CustomParamsSchema> = {
  name: 'custom-mcp-server',
  description: 'Mô tả chức năng của MCP server này.',
  params: CustomParamsSchema,
  cb: async (params) => {
    // params đã được zod xác thực và có kiểu suy luận từ CustomParamsSchema
    const { userId, limit } = params

    console.log(`Processing request for user ${userId} with limit ${limit}`)

    // Thực hiện logic xử lý bất đồng bộ
    // const result = await someAsyncOperation(userId, limit);

    // Trả về kết quả theo format yêu cầu
    return {
      content: [
        {
          type: 'text',
          text: `Kết quả xử lý cho ${userId}.` // Thay bằng nội dung kết quả thực tế
        }
        // Có thể thêm các content block khác (ví dụ: type: 'tool_code')
      ]
    }
  }
}
```

**4. Đăng ký MCP Server**

- Mở file `packages/server/src/mcp/index.ts`.
- Import đối tượng MCP server vừa tạo từ file implementation tương ứng.

  TypeScript

  ```typescript
  import { CustomMCPServer } from './server/custom-mcp-server' // Thay tên file và object tương ứng
  ```

- Trong hàm `registerMCPServers`, sử dụng hàm `addMcpServer` được truyền vào để đăng ký đối tượng server mới.

  TypeScript

  ```typescript
  export function registerMCPServers(addMcpServer: (mcpServer: MCPServer<any>) => void) {
    // Đăng ký các server hiện có
    // addMcpServer(ExistingMCPServer);

    // Đăng ký server mới
    addMcpServer(CustomMCPServer)
  }
  ```

**5. Biên dịch và Khởi động lại Server**

- Biên dịch lại mã nguồn TypeScript của project.
- Khởi động lại application server để các thay đổi và MCP server mới được áp dụng.
- Ấn reload button trong node MCP Client sẽ thấy MCP mới được hiển thị tại đây.
