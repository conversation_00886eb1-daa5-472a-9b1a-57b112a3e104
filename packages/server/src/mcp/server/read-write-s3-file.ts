import { MCPServer } from '../types'
import { z } from 'zod'
import { GetObjectCommand, HeadObjectCommand, ListObjectsV2Command, PutObjectCommand, S3Client } from '@aws-sdk/client-s3'

export const ReadFileMCPServer: MCPServer<any> = {
  name: 'read-s3-files-by-prefixes',
  description:
    'Đọc nội dung của các tệp tin được lưu trữ trên <PERSON>ho tà<PERSON> li<PERSON> (chỉ tệp văn bản). <PERSON><PERSON> thể chỉ định các đường dẫn đến tệp tin/thư mục cụ thể.',
  params: {
    prefixes: z.array(z.string())
  },
  cb: async ({ prefixes }: { prefixes: string[] }) => {
    const bucket = process.env.S3_STORAGE_BUCKET_NAME
    const region = process.env.S3_STORAGE_REGION || 'us-east-1'

    if (!bucket) {
      return {
        content: [
          {
            type: 'text',
            text: 'Error: AWS_BUCKET_NAME environment variable is not set.'
          }
        ]
      }
    }

    const s3Client = new S3Client({
      region,
      ...(process.env.AWS_ACCESS_KEY_ID
        ? {
            credentials: {
              accessKeyId: process.env.AWS_ACCESS_KEY_ID,
              secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
            }
          }
        : {})
    })
    let allFilesContent: Array<{
      filePath: string
      content: string
    }> = []

    try {
      for (let prefix of prefixes) {
        // ——— Normalize prefix ———
        // If the client passed "my-bucket/some/path" or "my-bucketsome/path",
        // strip off the bucket name (and optional slash) so we end up with "some/path"
        if (prefix.startsWith(bucket + '/')) {
          prefix = prefix.slice(bucket.length + 1)
        } else if (prefix.startsWith(bucket)) {
          prefix = prefix.slice(bucket.length)
        }

        // First, check if the prefix exists as a file directly
        try {
          const getObjectParams = {
            Bucket: bucket,
            Key: prefix
          }

          const getObjectCommand = new GetObjectCommand(getObjectParams)
          const fileResponse = await s3Client.send(getObjectCommand)

          if (fileResponse.Body) {
            // It's a file, read its content
            const fileContent = await fileResponse.Body.transformToString()
            allFilesContent.push({
              filePath: prefix,
              content: fileContent
            })
            continue // Move to next prefix
          }
        } catch (error) {
          // Not a direct file, might be a folder or doesn't exist
          // Continue to check if it's a folder
        }

        // Check if it's a folder by listing objects with this prefix
        const folderPrefix = prefix.endsWith('/') ? prefix : `${prefix}/`
        const listParams = {
          Bucket: bucket,
          Prefix: folderPrefix
        }

        const listCommand = new ListObjectsV2Command(listParams)
        const listResponse = await s3Client.send(listCommand)

        if (listResponse.Contents && listResponse.Contents.length > 0) {
          // It's a folder with files
          for (const file of listResponse.Contents) {
            if (file.Key && file.Key !== folderPrefix) {
              try {
                const getObjectParams = {
                  Bucket: bucket,
                  Key: file.Key
                }

                const getObjectCommand = new GetObjectCommand(getObjectParams)
                const fileResponse = await s3Client.send(getObjectCommand)

                if (fileResponse.Body) {
                  // Attempt to read as text
                  try {
                    const fileContent = await fileResponse.Body.transformToString()
                    allFilesContent.push({
                      filePath: file.Key,
                      content: fileContent
                    })
                  } catch (readError) {
                    // Not a text file or error reading
                    allFilesContent.push({
                      filePath: file.Key,
                      content: `Error reading file: ${(readError as Error).message}`
                    })
                  }
                }
              } catch (fileError) {
                allFilesContent.push({
                  filePath: file.Key || 'unknown',
                  content: `Error accessing file: ${(fileError as Error).message}`
                })
              }
            }
          }
        } else {
          // Neither a file nor a folder with contents
          allFilesContent.push({
            filePath: prefix,
            content: `Error: Path '${prefix}' not found or empty.`
          })
        }
      }
    } catch (error) {
      allFilesContent.push({
        filePath: '',
        content: `Error reading file: ${(error as Error).message}`
      })
    }

    return {
      content: [
        {
          type: 'text',
          text:
            allFilesContent.map((v) => `- File: ${JSON.stringify(v.filePath)}\n- Content: ${JSON.stringify(v.content)}`).join('\n') ||
            'No files found or no content processed. Check prefixes or logs.'
        }
      ]
    }
  }
}

export const WriteFileMCPServer: MCPServer<any> = {
  name: 'write-s3-files',
  description: 'Ghi các tệp văn bản lên S3. Nhận một mảng gồm các đối tượng { filePath, content }.',
  params: {
    files: z.array(z.object({ filePath: z.string(), content: z.string() }))
  },
  cb: async ({ files }: { files: { filePath: string; content: string }[] }) => {
    const bucket = process.env.S3_STORAGE_BUCKET_NAME
    const region = process.env.S3_STORAGE_REGION || 'us-east-1'

    if (!bucket) {
      return {
        content: [{ type: 'text', text: 'Error: S3_STORAGE_BUCKET_NAME environment variable is not set.' }]
      }
    }

    const s3Client = new S3Client({
      region,
      ...(process.env.AWS_ACCESS_KEY_ID
        ? {
            credentials: {
              accessKeyId: process.env.AWS_ACCESS_KEY_ID,
              secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
            }
          }
        : {})
    })

    const results: string[] = []

    for (const { filePath, content } of files) {
      let key = filePath
      if (key.startsWith(bucket + '/')) {
        key = key.slice(bucket.length + 1)
      } else if (key === bucket) {
        results.push(`❌ Invalid filePath '${filePath}': cannot write to bucket root or bucket name.`)
        continue
      } else if (key.startsWith(bucket)) {
        key = key.slice(bucket.length)
      }

      // — Prevent creating files at bucket root (must include a folder) —
      if (!key.includes('/')) {
        results.push(
          `❌ Invalid filePath '${filePath}': Cannot create files at the bucket root. ` +
            `Please include a folder path, e.g., 'folder1/file.txt'.`
        )
        continue
      }

      // — Ensure the target folder prefix actually exists —
      const folderPath = key.substring(0, key.lastIndexOf('/') + 1)
      try {
        const folderList = await s3Client.send(new ListObjectsV2Command({ Bucket: bucket, Prefix: folderPath, MaxKeys: 1 }))
        if (!folderList.Contents || folderList.Contents.length === 0) {
          results.push(`❌ Cannot create '${key}': Folder '${folderPath}' does not exist. ` + `Please create the folder first.`)
          continue
        }
      } catch (folderErr: any) {
        results.push(`❌ Error checking folder '${folderPath}': ${folderErr.message}`)
        continue
      }

      // Check existence to prevent overwrite
      try {
        await s3Client.send(new HeadObjectCommand({ Bucket: bucket, Key: key }))
        results.push(`❌ File '${key}' already exists. Overwriting is not allowed.`)
        continue
      } catch (headErr: any) {
        // Only proceed if error code is 404 (Not Found)
        if (headErr.$metadata?.httpStatusCode !== 404) {
          results.push(`❌ Error checking existence of '${key}': ${headErr.message}`)
          continue
        }
      }

      // Safe to write new file
      try {
        const putCommand = new PutObjectCommand({
          Bucket: bucket,
          Key: key,
          Body: content
        })
        await s3Client.send(putCommand)
        results.push(`✅ Wrote: ${key}`)
      } catch (error) {
        results.push(`❌ Error writing ${key}: ${(error as Error).message}`)
      }
    }

    return {
      content: [
        {
          type: 'text',
          text: results.join('\n') || 'No files processed.'
        }
      ]
    }
  }
}
