import { MCPServer } from '../types'
import { z } from 'zod'
import { AmazonKnowledgeBaseRetriever } from '@langchain/aws'
import { RetrievalFilter } from '@aws-sdk/client-bedrock-agent-runtime'
import * as convert from 'xml-js'
import { sessionData } from '../session'

export const KnowledgeBaseRetrieverMCPServer: MCPServer<any> = {
  name: 'knowledge-base-retriever',
  description: '<PERSON><PERSON><PERSON> xuất thông tin từ kho tri thức dựa trên các tham số truy vấn.',
  params: {
    query: z.string().describe('The search query to retrieve relevant information'),
    prefixes: z.array(z.string()).optional().describe('S3 prefixes to filter the search results'),
    topK: z.number().optional().describe('Override the maximum number of documents to retrieve'),
    searchType: z.enum(['HYBRID', 'SEMANTIC']).optional().describe('Override the search type (HYBRID or SEMANTIC)'),
    knowledgeBaseId: z.string().optional().describe('Knowledge Base ID to retrieve information from')
  },
  cb: async (
    {
      query,
      prefixes,
      topK,
      searchType,
      knowledgeBaseId
    }: {
      query: string
      prefixes?: string[]
      topK?: number
      searchType?: 'HYBRID' | 'SEMANTIC'
      knowledgeBaseId?: string
    },
    { sessionId }
  ) => {
    const { userId, groupId } = sessionData[sessionId]

    // 1) build filter
    const filter: RetrievalFilter | undefined =
      prefixes && prefixes.length > 0
        ? prefixes.length > 1
          ? {
              orAll: prefixes.map((prefix) => ({
                stringContains: { key: 'x-amz-bedrock-kb-source-uri', value: prefix }
              }))
            }
          : {
              stringContains: {
                key: 'x-amz-bedrock-kb-source-uri',
                value: prefixes[0]
              }
            }
        : undefined

    // 2) load AWS config from env or params
    const region = process.env.AWS_REGION ?? 'us-east-1'
    const knowledgeBaseIdValue = knowledgeBaseId ?? process.env.KNOWLEDGE_BASE_ID ?? ''
    const topKValue = topK ?? parseInt(process.env.KNOWLEDGE_BASE_RETRIEVER_TOPK ?? '10', 10)
    const overrideSearchType: 'HYBRID' | 'SEMANTIC' | undefined =
      searchType ?? ((process.env.AWS_BEDROCK_SEARCH_TYPE as 'HYBRID' | 'SEMANTIC') || undefined)

    // 3) instantiate retriever
    const retriever = new AmazonKnowledgeBaseRetriever({
      region,
      knowledgeBaseId: knowledgeBaseIdValue,
      topK: topKValue,
      filter,
      overrideSearchType,
      clientOptions: {
        credentials: process.env.AWS_ACCESS_KEY_ID
          ? {
              accessKeyId: process.env.AWS_ACCESS_KEY_ID,
              secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
              sessionToken: process.env.AWS_SESSION_TOKEN
            }
          : undefined
      }
    })

    // 4) perform retrieval & measure time
    const start = Date.now()
    const documents = await retriever.invoke(query)
    const processingTime = Date.now() - start

    // 5) shape results
    const results = documents
      .filter((doc) => {
        const sourceUri: string = doc.metadata?.['x-amz-bedrock-kb-source-uri'] || ''

        if (sourceUri.includes('/users/') && !sourceUri.includes(`/users/${userId}`)) {
          return false
        }

        if (sourceUri.includes('/groups/') && !sourceUri.includes(`/groups/${groupId}`)) {
          return false
        }

        return true
      })
      .map((doc) => ({
        text: doc.pageContent,
        metadata: doc.metadata
      }))

    const retrievedData = {
      results,
      metadata: { totalResults: results.length, processingTime }
    }

    const xmlData = convert.json2xml(JSON.stringify(retrievedData), { compact: true, spaces: 2 })

    return {
      content: [
        {
          type: 'text',
          text: xmlData
        }
      ]
    }
  }
}
