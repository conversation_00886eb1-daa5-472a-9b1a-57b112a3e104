import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc.js'
import timezone from 'dayjs/plugin/timezone.js'
import { MCPServer } from '../types'
import { z } from 'zod'

dayjs.extend(utc)
dayjs.extend(timezone)

export const GetCurrentTimeMCPServer: MCPServer<any> = {
  name: 'get-current-time',
  description: 'Lấy thời gian hiện tại theo timezone (timezone mặc định là Asia/Ho_Chi_Minh)',
  params: {
    timezone: z.string().optional().default('Asia/Ho_Chi_Minh')
  },
  cb: async ({ timezone }: any) => {
    const currentTime = dayjs()
      .tz(timezone || 'Asia/Ho_Chi_Minh')
      .format('YYYY-MM-DD HH:mm:ss')

    return {
      content: [
        {
          type: 'text',
          text: `Thời gian hiện tại theo giờ Việt Nam là ${currentTime}`
        }
      ]
    }
  }
}
