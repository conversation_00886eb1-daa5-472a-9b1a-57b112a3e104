import {
  S3Client,
  ListObjectsV2Command,
  PutO<PERSON>Command,
  GetO<PERSON>Command,
  DeleteObjectCommand,
  ListObjectsV2CommandOutput,
  PutObjectCommandOutput,
  GetObjectCommandOutput,
  DeleteObjectCommandOutput
} from '@aws-sdk/client-s3'
import { NodeHttpHandler } from '@aws-sdk/node-http-handler'

const s3Client = new S3Client({
  credentials: {
    accessKeyId: process.env.BEDROCK_ACCESS_KEY_ID! || process.env.S3_STORAGE_ACCESS_KEY_ID! || process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey:
      process.env.BEDROCK_SECRET_ACCESS_KEY! || process.env.S3_STORAGE_SECRET_ACCESS_KEY! || process.env.AWS_SECRET_ACCESS_KEY!
  },
  region: process.env.S3_STORAGE_REGION || 'us-east-1',
  requestHandler: new NodeHttpHandler({ connectionTimeout: 600000 })
})

const BUCKET_NAME = process.env.S3_STORAGE_BUCKET_NAME || 'unknown'

// Helper to list objects
export async function listS3Objects(prefix = '', continuationToken?: string): Promise<ListObjectsV2CommandOutput> {
  const cmd = new ListObjectsV2Command({
    Bucket: BUCKET_NAME,
    Prefix: prefix,
    ContinuationToken: continuationToken,
    Delimiter: '/'
  })
  return s3Client.send(cmd)
}

// Helper to upload an object
export async function uploadS3Object(
  key: string,
  body: Buffer | Uint8Array | Blob | string,
  contentType: string
): Promise<PutObjectCommandOutput> {
  const cmd = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: body,
    ContentType: contentType
  })
  return s3Client.send(cmd)
}

// Helper to get an object
export async function getS3Object(key: string): Promise<GetObjectCommandOutput> {
  const cmd = new GetObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key
  })
  return s3Client.send(cmd)
}

// Helper to delete an object
export async function deleteS3Object(key: string): Promise<DeleteObjectCommandOutput> {
  const cmd = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key
  })
  return s3Client.send(cmd)
}
