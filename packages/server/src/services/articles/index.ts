import { StatusCodes } from 'http-status-codes'
import { Article } from '../../database/entities/Article'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getErrorMessage } from '../../errors/utils'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { Between } from 'typeorm'
import { Dayjs } from 'dayjs'

const createArticles = async (body: any): Promise<Article> => {
  try {
    console.log('body', body)
    const appServer = getRunningExpressApp()
    const articlesRepo = appServer.AppDataSource.getRepository(Article)
    if (!body) {
      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Invalid request body`)
    }
    const savedArticles = await articlesRepo.save(body)
    return savedArticles
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: articlesService.createArticles - ${getErrorMessage(error)}`)
  }
}

const getAllArticles = async (category: string = 'chung-khoan', date: Dayjs, limit: number = 10): Promise<Article[]> => {
  try {
    const appServer = getRunningExpressApp()

    const start = date.startOf('day').toDate()
    const end = date.endOf('day').toDate()

    const dbResponse = await appServer.AppDataSource.getRepository(Article).find({
      where: {
        category,
        createdDate: Between(start, end)
      },
      order: { createdDate: 'DESC' },
      take: limit
    })

    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: articlesService.getAllArticles - ${getErrorMessage(error)}`)
  }
}

export default {
  createArticles,
  getAllArticles
}
