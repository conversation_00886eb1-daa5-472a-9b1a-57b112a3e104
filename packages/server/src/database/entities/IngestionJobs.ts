import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, <PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm'
import { ChatFlow } from './ChatFlow'

@Entity('knowledge_base')
export class KnowledgeBase {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'varchar', nullable: false })
  knowledge_base_id: string

  @Column({ type: 'varchar', nullable: true })
  data_source_ids: string

  @Column({ type: 'varchar', nullable: true })
  folder_name: string

  @Column({ type: 'varchar', length: 255, nullable: true })
  index_name: string

  @OneToMany(() => ChatFlow, (chatflow) => chatflow.knowledgeBase)
  chatflows: ChatFlow[]

  @CreateDateColumn({ type: 'timestamp' })
  created_date: Date

  @UpdateDateColumn({ type: 'timestamp' })
  updated_date: Date
}
