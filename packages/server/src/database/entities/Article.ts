/* eslint-disable */
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

@Entity()
export class Article {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ nullable: true, type: 'text', unique: true })
  title: string

  @Column({ type: 'text' })
  link: boolean

  @Column({ nullable: true, type: 'text' })
  relation: string

  @Column({ nullable: true, type: 'text' })
  author?: string

  @Column({ type: 'text' })
  content: string

  @Column({ nullable: true, type: 'text' })
  summary_content: string

  @Column({ type: 'text' })
  category: string

  @Column({ type: 'timestamp' })
  @CreateDateColumn()
  createdDate: Date

  @Column({ type: 'timestamp' })
  @UpdateDateColumn()
  updatedDate: Date
}
