import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTableKnowledgeBase1746515646975 implements MigrationInterface {
  name = 'AddTableKnowledgeBase1746515646975'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "knowledge_base" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "knowledge_base_id" character varying NOT NULL, "data_source_ids" character varying, "folder_name" character varying, "index_name" character varying(255), "created_date" TIMESTAMP NOT NULL DEFAULT now(), "updated_date" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_19d3f52f6da1501b7e235f1da5c" PRIMARY KEY ("id"))`
    )
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "knowledgeBaseId" uuid`)
    await queryRunner.query(
      `ALTER TABLE "chat_flow" ADD CONSTRAINT "FK_86b752e5e379ee63bd0bf02870f" FOREIGN KEY ("knowledgeBaseId") REFERENCES "knowledge_base"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP CONSTRAINT "FK_86b752e5e379ee63bd0bf02870f"`)
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "knowledgeBaseId"`)
    await queryRunner.query(`DROP TABLE "knowledge_base"`)
  }
}
