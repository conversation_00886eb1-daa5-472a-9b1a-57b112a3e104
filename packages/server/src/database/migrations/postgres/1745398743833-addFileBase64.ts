import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddFileBase641745398743833 implements MigrationInterface {
  name = 'AddFileBase641745398743833'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message" ADD "fileBase64" text`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "fileBase64"`)
  }
}
