import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateFieldScanEd1747626516702 implements MigrationInterface {
  name = 'UpdateFieldScanEd1747626516702'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message" ADD "scanEd" boolean`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "scanEd"`)
  }
}
