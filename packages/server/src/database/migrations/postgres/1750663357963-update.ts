import { MigrationInterface, QueryRunner } from 'typeorm'

export class Update1750663357963 implements MigrationInterface {
  name = 'Update1750663357963'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "custom_template" DROP CONSTRAINT "FK_5385ee90acfc9b2220e1fa8c05b"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP CONSTRAINT "FK_1635951d0fed031d2cf0faf7ad4"`)
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "isTemplate"`)
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "originalTemplateId"`)
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "usageCount"`)
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "domain"`)
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "nodesSummary"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP COLUMN "category_id"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP COLUMN "is_community"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP COLUMN "original_template_id"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP COLUMN "usage_count"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP COLUMN "is_agentflow"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP COLUMN "template_source"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP COLUMN "nodes_summary"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP COLUMN "domain"`)
    await queryRunner.query(`ALTER TABLE "custom_template" DROP COLUMN "short_description"`)
    await queryRunner.query(`ALTER TABLE "chat_message" ADD "responseTime" double precision`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "responseTime"`)
    await queryRunner.query(`ALTER TABLE "custom_template" ADD "short_description" text`)
    await queryRunner.query(`ALTER TABLE "custom_template" ADD "domain" character varying(50)`)
    await queryRunner.query(`ALTER TABLE "custom_template" ADD "nodes_summary" text`)
    await queryRunner.query(`ALTER TABLE "custom_template" ADD "template_source" character varying(255)`)
    await queryRunner.query(`ALTER TABLE "custom_template" ADD "is_agentflow" boolean NOT NULL DEFAULT false`)
    await queryRunner.query(`ALTER TABLE "custom_template" ADD "usage_count" integer NOT NULL DEFAULT '0'`)
    await queryRunner.query(`ALTER TABLE "custom_template" ADD "original_template_id" uuid`)
    await queryRunner.query(`ALTER TABLE "custom_template" ADD "is_community" boolean NOT NULL DEFAULT false`)
    await queryRunner.query(`ALTER TABLE "custom_template" ADD "category_id" uuid`)
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "nodesSummary" text`)
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "domain" text`)
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "usageCount" integer NOT NULL DEFAULT '0'`)
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "originalTemplateId" uuid`)
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "isTemplate" boolean NOT NULL DEFAULT false`)
    await queryRunner.query(
      `ALTER TABLE "custom_template" ADD CONSTRAINT "FK_1635951d0fed031d2cf0faf7ad4" FOREIGN KEY ("original_template_id") REFERENCES "custom_template"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
    )
    await queryRunner.query(
      `ALTER TABLE "custom_template" ADD CONSTRAINT "FK_5385ee90acfc9b2220e1fa8c05b" FOREIGN KEY ("category_id") REFERENCES "template_categories"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
    )
  }
}
