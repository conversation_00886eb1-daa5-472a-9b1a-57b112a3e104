import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTableGroupUsersFieldDisplayPrefixes1746516330761 implements MigrationInterface {
  name = 'UpdateTableGroupUsersFieldDisplayPrefixes1746516330761'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_users" ADD "displayPrefixes" character varying`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "group_users" DROP COLUMN "displayPrefixes"`)
  }
}
