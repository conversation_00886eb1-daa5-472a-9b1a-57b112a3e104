import dayjs from 'dayjs'
import { NextFunction, Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import articlesService from '../../services/articles'

const createArticles = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.body) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, `Error: articlesController.createArticles - body not provided!`)
    }
    const apiResponse = await articlesService.createArticles(req.body)
    return res.json(apiResponse)
  } catch (error) {
    next(error)
  }
}

const getAllArticles = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { date, category }: any = req.query
    if (!category) {
      throw new Error('Missing category')
    }
    const apiResponse = await articlesService.getAllArticles(category, dayjs(date || Date.now()))
    return res.json(apiResponse)
  } catch (error) {
    next(error)
  }
}

export default {
  createArticles,
  getAllArticles
}
