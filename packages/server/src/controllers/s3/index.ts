import { Request, Response } from 'express'
import { deleteS3Object, getS3Object, listS3Objects, uploadS3Object } from '../../services/s3Service'

// Controller to list objects in S3
export async function listObjectsController(req: Request, res: Response) {
  try {
    const { prefix, continuationToken } = req.query
    const result = await listS3Objects(prefix as string, continuationToken as string)
    res.status(200).json(result)
  } catch (error: any) {
    res.status(500).json({ error: error.message })
  }
}

// Controller to upload an object to S3
export async function uploadObjectController(req: Request, res: Response) {
  try {
    const { key, contentType } = req.body
    const body = req.file?.buffer || req.body.body // Assuming file upload middleware is used
    if (!key || !body || !contentType) {
      return res.status(400).json({ error: 'Missing required fields' })
    }
    const result = await uploadS3Object(key, body, contentType)
    res.status(200).json(result)
  } catch (error: any) {
    res.status(500).json({ error: error.message })
  }
}

// Controller to get an object from S3
export async function getObjectController(req: Request, res: Response) {
  try {
    const { key } = req.params
    if (!key) {
      return res.status(400).json({ error: 'Key is required' })
    }
    const result = await getS3Object(key)
    res.status(200).json(result)
  } catch (error: any) {
    res.status(500).json({ error: error.message })
  }
}

// Controller to delete an object from S3
export async function deleteObjectController(req: Request, res: Response) {
  try {
    const { key } = req.params
    if (!key) {
      return res.status(400).json({ error: 'Key is required' })
    }
    const result = await deleteS3Object(key)
    res.status(200).json(result)
  } catch (error: any) {
    res.status(500).json({ error: error.message })
  }
}
