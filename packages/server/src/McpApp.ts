import express from 'express'
import { randomUUID } from 'node:crypto'
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js'
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js'
import { isInitializeRequest } from '@modelcontextprotocol/sdk/types.js'
// noinspection ES6PreferShortImport
import { registerMCPServers } from './mcp/index'
import logger from './utils/logger'
import { sessionData } from './mcp/session'

const app = express()
app.use(express.json())

// Map to store transports by session ID
const transports: { [sessionId: string]: StreamableHTTPServerTransport } = {}

// Handle POST requests for client-to-server communication
app.post('/mcp', async (req, res) => {
  const userId = req.query.userId as string
  const groupId = req.query.groupId as string

  // Check for existing session ID
  const sessionId = req.headers['mcp-session-id'] as string | undefined
  let transport: StreamableHTTPServerTransport

  if (sessionId) {
    sessionData[sessionId] = { userId, groupId }
    setTimeout(() => {
      delete sessionData[sessionId]
    }, 60000 * 60)
  }

  if (sessionId && transports[sessionId]) {
    // Reuse existing transport
    transport = transports[sessionId]
  } else if (!sessionId && isInitializeRequest(req.body)) {
    // New initialization request
    transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: () => randomUUID(),
      onsessioninitialized: (sessionId) => {
        // Store the transport by session ID
        transports[sessionId] = transport
      }
    })

    // Clean up transport when closed
    transport.onclose = () => {
      if (transport.sessionId) {
        delete transports[transport.sessionId]
      }
    }

    const server = new McpServer({
      name: 'c-agent-mcp-server',
      version: '1.0.0'
    })

    registerMCPServers((mcpServer) => {
      server.tool(mcpServer.name, mcpServer.description, mcpServer.params, (a, b) => {
        console.log('call MCP:', mcpServer.name, JSON.stringify(a, null, 2))
        return mcpServer.cb(a, b) as any
      })
    })

    // Connect to the MCP server
    await server.connect(transport)
  } else {
    // Invalid request
    res.status(400).json({
      jsonrpc: '2.0',
      error: {
        code: -32000,
        message: 'Bad Request: No valid session ID provided'
      },
      id: null
    })
    return
  }

  // Handle the request
  await transport.handleRequest(req, res, req.body)
})

// Reusable handler for GET and DELETE requests
const handleSessionRequest = async (req: express.Request, res: express.Response) => {
  const sessionId = req.headers['mcp-session-id'] as string | undefined
  if (!sessionId || !transports[sessionId]) {
    res.status(400).send('Invalid or missing session ID')
    return
  }

  const transport = transports[sessionId]
  await transport.handleRequest(req, res)
}

// Handle GET requests for server-to-client notifications via SSE
app.get('/mcp', handleSessionRequest)

// Handle DELETE requests for session termination
app.delete('/mcp', handleSessionRequest)

export function startMCPServer() {
  app.listen(+(process.env.PORT || '3000') + 1, () => {
    logger.info(`⚡️ [MCP server]: MCP Server is listening at :${+(process.env.PORT || '3000') + 1}`)
  })
}
