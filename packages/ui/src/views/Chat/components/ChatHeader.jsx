import { useSidebar } from '@/views/Chat/hooks/ChatHook'
import { AppBar, IconButton, Stack, Toolbar, Tooltip, Typography } from '@mui/material'
import { IconEdit, IconLayoutSidebar } from '@tabler/icons-react'
import clsx from 'clsx'
import { memo } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

const ChatHeader = () => {
  const navigate = useNavigate()

  const { chatFlowId, chatSessionId } = useParams()

  const user = useSelector((state) => state.user)
  const [sidebar, setSidebar] = useSidebar()

  return (
    <AppBar
      position='static'
      open={sidebar.open}
      sx={{ background: '#ffffff', boxShadow: 'none', borderBottom: '1px solid #0d0d0d0d' }}
      className='w-full'
    >
      <Toolbar className='p-2 h-[56px]'>
        <Stack gap={1} direction='row' alignItems='center'>
          <Tooltip title={sidebar.open ? 'Đóng sidebar' : 'Mở sidebar'}>
            <IconButton color='inherit' onClick={() => setSidebar({ open: !sidebar.open })}>
              <IconLayoutSidebar color='#5d5d5d' />
            </IconButton>
          </Tooltip>
          <Tooltip title='Đoạn chat mới' className={clsx((sidebar.open || !chatFlowId) && 'hidden')}>
            <IconButton color='inherit' onClick={() => chatSessionId && navigate(`/chat/${chatFlowId}`)}>
              <IconEdit color='#5d5d5d' />
            </IconButton>
          </Tooltip>
        </Stack>
        <div className='flex-1' />
        <Typography variant='h4' color='#5d5d5d'>
          {user?.username || ''}
        </Typography>
      </Toolbar>
    </AppBar>
  )
}

export default memo(ChatHeader)
