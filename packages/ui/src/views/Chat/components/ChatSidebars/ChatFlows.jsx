import ComponentLoader from '@/ui-component/loading/ComponentLoader'
import { useFlows } from '@/views/Chat/hooks/ChatHook'
import { Avatar, List, ListItemAvatar, ListItemButton, ListItemText, ListSubheader } from '@mui/material'
import clsx from 'clsx'
import { memo, useEffect, useRef } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router'
import { ListBgColorWithTextWhite } from './data'

const ChatFlows = () => {
  const selectedItemRef = useRef(null)

  const location = useLocation()
  const navigate = useNavigate()
  const { chatFlowId, chatSessionId } = useParams()

  const [chatFlows] = useFlows()

  useEffect(() => {
    if (selectedItemRef.current) {
      selectedItemRef.current.scrollIntoView({
        block: 'center'
      })
    }
  }, [chatFlowId, chatSessionId])

  return (
    <List
      className={clsx(
        'p-2 pt-0 relative',
        chatFlowId ? 'max-h-[calc(100vh-70%)] min-h-[calc(100vh-70%)]' : 'max-h-[calc(100vh-56px)] min-h-[calc(100vh-56px)]'
      )}
      sx={{
        overflowY: 'auto',
        overflowX: 'hidden',
        scrollbarColor: '#d0d0d0 #ffffff00'
      }}
      subheader={
        <ListSubheader component='div' className='bg-[#ebebeb] p-0 leading-8'>
          Danh sách Agent
        </ListSubheader>
      }
    >
      {chatFlows?.map((flow, i) => {
        const isSelected = location.pathname.includes(flow.id)
        return (
          <ListItemButton
            ref={isSelected ? selectedItemRef : null}
            key={flow.id}
            title={flow.name}
            disableRipple={true}
            sx={{
              padding: '8px',
              borderRadius: '8px',
              background: isSelected ? '#d0d0d0' : 'transparent',
              pointerEvents: isSelected ? 'none' : 'auto',
              '&:hover': {
                background: '#e1e1e1'
              }
            }}
            onClick={() => !isSelected && navigate(`/chat/${flow.id}`)}
          >
            <ListItemAvatar className='min-w-11 m-0'>
              <Avatar
                variant='rounded'
                sx={{
                  bgcolor: ListBgColorWithTextWhite[i % ListBgColorWithTextWhite.length],
                  color: 'white',
                  width: '32px',
                  height: '32px',
                  fontSize: '12px'
                }}
              >
                {flow.name
                  .toUpperCase()
                  .replace(/[^A-Z1-9]/g, '')
                  .slice(0, 3)}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              sx={{
                '& .MuiTypography-root': {
                  display: 'block',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  fontSize: '16px'
                }
              }}
              className='m-0'
            >
              {flow.name}
            </ListItemText>
          </ListItemButton>
        )
      })}
      <ComponentLoader transparent={true} loading={!chatFlows?.length} />
    </List>
  )
}

export default memo(ChatFlows)
