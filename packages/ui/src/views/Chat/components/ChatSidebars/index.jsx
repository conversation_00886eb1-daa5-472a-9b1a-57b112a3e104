import LogoCMC720p from '@/assets/images/Logo-CMC-720p.png'
import { useSidebar, useFlows } from '@/views/Chat/hooks/ChatHook'
import {
  Box,
  Drawer,
  IconButton,
  Stack,
  Tooltip,
  useMediaQuery,
  useTheme,
  Typography,
  Divider,
  List,
  ListItemButton,
  ListItemText,
  Collapse,
  ListSubheader
} from '@mui/material'
import { IconEdit, IconSearch, IconChevronDown, IconChevronRight, IconLanguage, IconRobot } from '@tabler/icons-react'
import clsx from 'clsx'
import { Fragment, memo, useCallback, useState, useEffect } from 'react'
import { useNavigate, useParams, useLocation } from 'react-router'
import { useTranslation } from 'react-i18next'
import ChatFlows from './ChatFlows'
import ChatSessions from './ChatSessions'
import DialogFindChatFlow from './DialogFindChatFlow'

const ChatSidebars = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()

  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'))

  const { chatFlowId, chatSessionId } = useParams()

  const [sidebar, setSidebar] = useSidebar()
  const [chatFlows] = useFlows()

  const [dialogFindInfo, setDialogFindInfo] = useState({
    open: false
  })

  const [expandedSections, setExpandedSections] = useState({
    chatflows: true
  })

  useEffect(() => {
    // Navigate to translation by default if no specific route
    if (location.pathname === '/chat' || location.pathname === '/chat/') {
      navigate('/chat/translation')
    }
  }, [location.pathname, navigate])

  const handleDialogFindInfo = useCallback((value) => setDialogFindInfo((prev) => ({ ...prev, ...value })), [])

  const toggleSection = useCallback((section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section]
    }))
  }, [])

  const backToChat = useCallback(() => {
    navigate('/chat')
  }, [navigate])

  const isTranslationSelected = location.pathname === '/chat/translation'

  return (
    <Fragment>
      <Drawer
        open={sidebar.open}
        variant={isMobile ? 'temporary' : 'persistent'}
        anchor='left'
        onClose={() => setSidebar({ open: false })}
        ModalProps={{
          keepMounted: true // Better performance on mobile
        }}
        sx={{
          width: 300,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 300,
            boxSizing: 'border-box',
            background: '#ebebeb',
            border: 'none'
          }
        }}
      >
        <Stack direction='row' justifyContent='space-between' alignItems='center' gap={2} className='px-3 h-[56px] min-h-[56px]'>
          <Box onClick={backToChat} className='cursor-pointer'>
            <img alt='logo' width={150} height={30} src='https://www.kyocera.co.jp/_assets2/img/logo.svg' />
          </Box>
          {/* <Stack direction='row' gap={1}>
            <Tooltip title={!chatFlowId ? 'Tìm kiếm Agent' : 'Tìm kiếm đoạn chat'}>
              <IconButton color='inherit' onClick={() => handleDialogFindInfo({ open: true, type: !chatFlowId ? 'flow' : 'session' })}>
                <IconSearch color='#5d5d5d' />
              </IconButton>
            </Tooltip>
            <Tooltip title='Đoạn chat mới' className={clsx(!chatFlowId && 'hidden')}>
              <IconButton color='inherit' onClick={() => chatSessionId && navigate(`/chat/${chatFlowId}`)}>
                <IconEdit color='#5d5d5d' />
              </IconButton>
            </Tooltip>
          </Stack> */}
        </Stack>

        {/* Main Content */}
        <Box sx={{ flex: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
          {/* Translation Service */}
          <List sx={{ py: 0, px: 1 }}>
            <ListItemButton
              selected={isTranslationSelected}
              onClick={() => navigate('/chat/translation')}
              sx={{
                px: 2,
                py: 1.5,
                borderRadius: '8px',
                '&:hover': { backgroundColor: 'rgba(0,0,0,0.04)' },
                '&.Mui-selected': {
                  backgroundColor: 'rgba(0,0,0,0.08)',
                  '&:hover': { backgroundColor: 'rgba(0,0,0,0.08)' }
                }
              }}
            >
              <IconLanguage size={20} color='#5d5d5d' style={{ marginRight: 12 }} />
              <ListItemText
                primary={t('sidebar.translation')}
                sx={{
                  '& .MuiTypography-root': {
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#333'
                  }
                }}
              />
            </ListItemButton>
          </List>

          <Divider sx={{ mx: 2, my: 1 }} />

          {/* Chatflows Section */}
          <Box sx={{ flex: 1, overflow: 'hidden' }}>
            <List sx={{ py: 0, px: 1 }}>
              <ListItemButton
                onClick={() => toggleSection('chatflows')}
                sx={{
                  px: 2,
                  py: 1,
                  borderRadius: '8px',
                  '&:hover': { backgroundColor: 'rgba(0,0,0,0.04)' }
                }}
              >
                {expandedSections.chatflows ? (
                  <IconChevronDown size={16} color='#5d5d5d' style={{ marginRight: 8 }} />
                ) : (
                  <IconChevronRight size={16} color='#5d5d5d' style={{ marginRight: 8 }} />
                )}
                <IconRobot size={20} color='#5d5d5d' style={{ marginRight: 12 }} />
                <ListItemText
                  primary={t('sidebar.aiAgents')}
                  sx={{
                    flex: 1,
                    '& .MuiTypography-root': {
                      fontSize: '14px',
                      fontWeight: 500,
                      color: '#333'
                    }
                  }}
                />
                {/* <Tooltip title='Tìm kiếm Agent'>
                  <IconButton
                    size='small'
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDialogFindInfo({ open: true })
                    }}
                    sx={{
                      p: 0.5,
                      '&:hover': { backgroundColor: 'rgba(0,0,0,0.08)' }
                    }}
                  >
                    <IconSearch size={16} color='#5d5d5d' />
                  </IconButton>
                </Tooltip> */}
              </ListItemButton>
            </List>

            <Collapse in={expandedSections.chatflows} timeout='auto' unmountOnExit>
              <Box sx={{ pl: 1 }}>
                <ChatFlows />
              </Box>
            </Collapse>
          </Box>

          {/* Chat Sessions for selected flow */}
          {chatFlowId && (
            <Box sx={{ borderTop: '1px solid #e0e0e0', pt: 1 }}>
              <ChatSessions />
            </Box>
          )}
        </Box>
      </Drawer>
      <DialogFindChatFlow open={dialogFindInfo.open} onClose={() => handleDialogFindInfo({ open: false })} />
    </Fragment>
  )
}

export default memo(ChatSidebars)
