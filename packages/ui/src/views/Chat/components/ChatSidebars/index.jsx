import LogoCMC720p from '@/assets/images/Logo-CMC-720p.png'
import { useSidebar } from '@/views/Chat/hooks/ChatHook'
import { Box, Drawer, IconButton, Stack, Tooltip, useMediaQuery, useTheme } from '@mui/material'
import { IconEdit, IconSearch } from '@tabler/icons-react'
import clsx from 'clsx'
import { Fragment, memo, useCallback, useState } from 'react'
import { useNavigate, useParams } from 'react-router'
import ChatFlows from './ChatFlows'
import ChatSessions from './ChatSessions'
import DialogFindChatFlow from './DialogFindChatFlow'

const ChatSidebars = () => {
  const navigate = useNavigate()

  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'))

  const { chatFlowId, chatSessionId } = useParams()

  const [sidebar, setSidebar] = useSidebar()

  const [dialogFindInfo, setDialogFindInfo] = useState({
    open: false
  })

  const handleDialogFindInfo = useCallback((value) => setDialogFindInfo((prev) => ({ ...prev, ...value })), [])

  const renderWithCondition = useCallback((condition, component) => {
    if (condition) return component
    return null
  }, [])

  const backToChat = useCallback(() => {
    navigate('/chat')
  }, [navigate])

  return (
    <Fragment>
      <Drawer
        open={sidebar.open}
        variant={isMobile ? 'temporary' : 'persistent'}
        anchor='left'
        onClose={() => setSidebar({ open: false })}
        sx={{
          width: 300,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 300,
            boxSizing: 'border-box',
            background: '#ebebeb',
            border: 'none'
          }
        }}
      >
        <Stack direction='row' justifyContent='space-between' alignItems='center' gap={2} className='px-3 h-[56px] min-h-[56px]'>
          <Box onClick={backToChat} className='cursor-pointer'>
            <img alt='logo' width={45} height={30} src={LogoCMC720p} />
          </Box>
          <Stack direction='row' gap={1}>
            <Tooltip title={!chatFlowId ? 'Tìm kiếm Agent' : 'Tìm kiếm đoạn chat'}>
              <IconButton color='inherit' onClick={() => handleDialogFindInfo({ open: true, type: !chatFlowId ? 'flow' : 'session' })}>
                <IconSearch color='#5d5d5d' />
              </IconButton>
            </Tooltip>
            <Tooltip title='Đoạn chat mới' className={clsx(!chatFlowId && 'hidden')}>
              <IconButton color='inherit' onClick={() => chatSessionId && navigate(`/chat/${chatFlowId}`)}>
                <IconEdit color='#5d5d5d' />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
        <ChatFlows />
        {renderWithCondition(chatFlowId, <ChatSessions />)}
      </Drawer>
      <DialogFindChatFlow open={dialogFindInfo.open} onClose={() => handleDialogFindInfo({ open: false })} />
    </Fragment>
  )
}

export default memo(ChatSidebars)
