/* eslint-disable jsx-a11y/no-autofocus */
import UploadButton from '@/ui-component/button/UploadButton'
import ComponentLoader from '@/ui-component/loading/ComponentLoader'
import DotLoading from '@/ui-component/loading/DotLoading'
import ModalPreviewImage from '@/ui-component/modal/ModalPreviewImage'
import { convertFileImageToBase64 } from '@/utils/genericHelper'
import { useChatSSE, useMessages } from '@/views/Chat/hooks/ChatHook'
import { Badge, Box, IconButton, InputAdornment, OutlinedInput, Paper, Stack, Typography } from '@mui/material'
import { IconArrowDown, IconFileUpload, IconSend2, IconX } from '@tabler/icons-react'
import clsx from 'clsx'
import { memo, useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import Markdown from 'react-markdown'
import { useParams } from 'react-router'

const ChatContent = () => {
  const refInput = useRef(null)
  const refCheckOnceRerender = useRef(false)
  const refChatContainer = useRef(null)
  const refBoxInput = useRef(null)

  const { chatFlowId } = useParams()

  const [chatMessages] = useMessages()
  const { response, handleRequest } = useChatSSE()

  const [uploads, setUploads] = useState([])
  const [userInput, setUserInput] = useState('')
  const [inputHeight, setInputHeight] = useState(0)
  const [showScrollButton, setShowScrollButton] = useState(false)
  const [previewImage, setPreviewImage] = useState({
    open: false,
    image: ''
  })

  const onChange = useCallback((e) => {
    setUserInput(e.target.value)
    if (refBoxInput.current) setInputHeight(refBoxInput.current.clientHeight)
  }, [])

  const onChangeUpload = async (e) => {
    if (e.target.files?.length) {
      const files = Array.from(e.target.files)
      const newFiles = []
      for await (const file of files) {
        const imageBase64 = await convertFileImageToBase64(file)
        const newFile = {
          name: file.name,
          data: imageBase64,
          mime: file.type,
          type: 'file'
        }
        newFiles.push(newFile)
      }
      setUploads((prev) => [...prev, ...newFiles])
      refInput.current?.focus()
    }
    if (refBoxInput.current) setInputHeight(refBoxInput.current.clientHeight)
  }

  const handleScroll = () => {
    if (!refChatContainer.current) return
    const { scrollTop, scrollHeight, clientHeight } = refChatContainer.current
    const isNotAtBottom = scrollHeight - scrollTop - clientHeight > 50
    if (isNotAtBottom !== showScrollButton) setShowScrollButton(isNotAtBottom)
  }

  const scrollToBottom = (smooth = true) => {
    if (refChatContainer.current) {
      refChatContainer.current.scrollTo({
        top: refChatContainer.current.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto'
      })
    }
  }

  const handleEnter = (e) => {
    if (e.key === 'Enter') {
      if (e.shiftKey && userInput.trim()) return
      e.preventDefault()
      if (userInput.trim() && response.status === 'free') {
        handleRequest({ question: userInput, uploads })
        setUserInput('')
        scrollToBottom()
        setUploads([])
        refInput.current?.focus()
      }
    }
  }

  const ViewStatusResponse = useMemo(() => {
    if (response.status === 'recheck') return 'Rechecking...'
    if (response.status !== 'free') return 'Đang xử lý...'
    return ''
  }, [response])

  const renderWithCondition = useCallback((condition, component) => {
    if (condition) return component
    return null
  }, [])

  useEffect(() => {
    if (refBoxInput.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setInputHeight(entry.target.clientHeight)
        }
      })

      resizeObserver.observe(refBoxInput.current)
      return () => resizeObserver.disconnect()
    }
  }, [])

  useEffect(() => {
    if (chatMessages.length > 0) {
      if (response.status === 'responding') scrollToBottom()
      else scrollToBottom(false)
    }
  }, [chatMessages.length, response])

  useEffect(() => {
    if (chatMessages?.length && !refCheckOnceRerender.current) {
      refInput.current?.focus()
      refCheckOnceRerender.current = true
    }
  }, [chatMessages])

  useLayoutEffect(() => {
    scrollToBottom(false)

    return () => {
      setShowScrollButton(false)
    }
  }, [])

  return (
    <Stack direction='column' alignItems='center' className={clsx('w-full h-[calc(100vh-57px)] relative', !chatFlowId && 'hidden')}>
      <Stack
        ref={refChatContainer}
        flexGrow={1}
        direction='column'
        onScroll={handleScroll}
        sx={{
          scrollbarWidth: 'none',
          '& p': {
            margin: 0
          }
        }}
        className={clsx('max-w-[56rem] w-full py-2 overflow-auto')}
      >
        {chatMessages?.map((chat, i) => {
          if (chat.type === 'abort')
            return (
              <Paper key={i} className='p-4 text-center'>
                <Typography className='text-[#a8a8a8]'>{chat.message}</Typography>
              </Paper>
            )
          if (chat.type === 'question')
            return (
              <Paper key={i} className='p-4'>
                {renderWithCondition(
                  !!chat.uploads?.length,
                  <Stack direction='row' flexWrap='wrap' gap={1} className={clsx('mb-2 ml-auto w-fit max-w-[90%]')}>
                    {chat.uploads?.map((file, i) => (
                      <Box
                        key={i}
                        width={64}
                        height={64}
                        display='flex'
                        alignItems='center'
                        sx={{
                          position: 'relative',
                          borderRadius: '8px',
                          border: '1px solid #e0e0e0',
                          cursor: 'pointer'
                        }}
                        onClick={() => setPreviewImage({ open: true, image: file.data })}
                      >
                        <img src={file.data} alt={file.name} className='object-contain w-full h-full' />
                      </Box>
                    ))}
                  </Stack>
                )}
                <Markdown className='ml-auto w-fit max-w-[90%] py-2 px-3 bg-[#3b81f6] text-white break-words rounded-md'>
                  {chat.message}
                </Markdown>
              </Paper>
            )
          if (chat.status === 'responding' && !chat.message)
            return (
              <Paper key={i} className='px-8 py-2'>
                <DotLoading />
              </Paper>
            )
          if (chat.message)
            return (
              <Paper key={i} className='p-4'>
                <Markdown className='mr-auto w-fit max-w-[90%] py-2 px-3 bg-[#f7f8ff] text-[#303235] break-words rounded-md'>
                  {chat.message}
                </Markdown>
              </Paper>
            )
          return null
        })}
      </Stack>

      {showScrollButton && (
        <IconButton
          onClick={scrollToBottom}
          sx={{
            position: 'absolute',
            bottom: `${inputHeight + 20}px`,
            left: '50%',
            transform: 'translateX(-50%)',
            backgroundColor: 'white',
            border: '1px solid #e0e0e0',
            zIndex: 1,
            transition: 'bottom 0.2s',
            '&:hover': {
              backgroundColor: '#f5f5f5'
            }
          }}
        >
          <IconArrowDown size={20} color='#3B81F6' />
        </IconButton>
      )}

      <Box className={clsx('bg-white px-4 pb-4 max-w-[56rem] w-full sticky bottom-0')}>
        <Typography className='text-[#a8a8a8] text-[14px] leading-4 bg-white h-4 w-full'>{ViewStatusResponse}</Typography>
        <Box ref={refBoxInput} className='w-full'>
          <Stack direction='row' flexWrap='wrap' gap={1} className={clsx('mb-2', !uploads?.length && 'hidden')}>
            {uploads?.map((file, i) => {
              const isImage = file.mime.startsWith('image/')

              if (isImage)
                return (
                  <Badge
                    key={i}
                    badgeContent={
                      <IconX
                        className='w-4 h-4 cursor-pointer'
                        onClick={() => setUploads((prev) => prev.filter((_, index) => index !== i))}
                      />
                    }
                    sx={{
                      '& .MuiBadge-badge': {
                        padding: '2px',
                        background: '#222222',
                        color: '#ffffff',
                        top: '6px',
                        right: '6px',
                        border: '2px solid #ffffff',
                        height: 'auto',
                        borderRadius: '50%'
                      }
                    }}
                  >
                    <Box
                      width={64}
                      height={64}
                      display='flex'
                      alignItems='center'
                      sx={{
                        position: 'relative',
                        borderRadius: '8px',
                        border: '1px solid #e0e0e0',
                        cursor: 'pointer'
                      }}
                      onClick={() => setPreviewImage({ open: true, image: file.data })}
                    >
                      <img src={file.data} alt={file.name} className='object-contain w-full h-full' />
                    </Box>
                  </Badge>
                )
              return null
            })}
          </Stack>
          <OutlinedInput
            ref={refInput}
            autoFocus
            multiline
            maxRows={7}
            placeholder='Nhập câu hỏi của bạn...'
            value={userInput}
            onChange={onChange}
            onKeyDown={handleEnter}
            className={clsx(!chatFlowId && 'hidden')}
            sx={{
              width: '100%',
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#e0e0e0'
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#3B81F6'
              },
              '& .MuiInputBase-input': {
                whiteSpace: 'pre-wrap',
                wordWrap: 'break-word'
              }
            }}
            startAdornment={
              <InputAdornment position='start' sx={{ height: 'auto', maxHeight: '100%', margin: 'auto 6px 6px' }}>
                <UploadButton type='icon' multiple={true} icon={<IconFileUpload />} onChange={onChangeUpload} />
              </InputAdornment>
            }
            endAdornment={
              <InputAdornment position='end' sx={{ height: 'auto', maxHeight: '100%', margin: 'auto 6px 6px' }}>
                <IconButton
                  type='submit'
                  sx={{
                    color: userInput.trim() ? '#3B81F6' : '#9e9e9e'
                  }}
                >
                  <IconSend2 />
                </IconButton>
              </InputAdornment>
            }
          />
        </Box>
      </Box>
      <ModalPreviewImage image={previewImage.image} open={previewImage.open} onClose={() => setPreviewImage({ open: false, image: '' })} />
      <ComponentLoader blur={false} loading={!chatFlowId || !chatMessages?.length} />
    </Stack>
  )
}

export default memo(ChatContent)
