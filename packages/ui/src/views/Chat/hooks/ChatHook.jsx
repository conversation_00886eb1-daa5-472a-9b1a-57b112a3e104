/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-constant-condition */
import { baseURL } from '@/store/constant'
import { usePrompt } from '@/utils/usePrompt'
import { ChatContext } from '@/views/Chat/context/ChatContext'
import { throttle } from 'lodash'
import { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useParams } from 'react-router-dom'
import { v4 } from 'uuid'

export const useAll = () => useContext(ChatContext)

export const useSidebar = () => {
  const { sidebar, handleSidebar } = useAll()

  return [sidebar, handleSidebar]
}

export const useFlows = () => {
  const { chatFlows, handleChatFlows } = useAll()

  return [chatFlows, handleChatFlows]
}

export const useSessions = () => {
  const { chatSessions, handleChatSessions, handleDeleteSession } = useAll()

  return [chatSessions, handleChatSessions, handleDeleteSession]
}

export const useMessages = () => {
  const { chatMessages, handleChatMessages } = useAll()

  return [chatMessages, handleChatMessages]
}

export const useChatSSE = () => {
  const abortController = useRef(null)

  const { chatFlowId, chatSessionId } = useParams()

  const [_, setMessages] = useMessages()

  const [answer, setAnswer] = useState('')
  const [response, setResponse] = useState({
    status: 'free', // free - waiting - recheck - responding
    error: null,
    chatFlowId,
    chatSessionId
  })
  const [request, setRequest] = useState({
    question: '',
    uploads: []
  })

  usePrompt('Tiến trình sẽ bị dừng nếu bạn rời khỏi trang này, bạn có muốn rời trang không?', response.status !== 'free', {
    callbackWhenConfirmed: () => {
      abortController.current?.abort()
      setResponse((prev) => ({ ...prev, status: 'free', error: null }))
      setAnswer('')
      abortController.current = null
    }
  })

  const handleMessage = throttle((message) => {
    if (!message) setMessages(null, { doneResponse: true })
    else
      setMessages(
        { message },
        {
          replaceRespondingMessage: true
        }
      )
  }, 200)

  const handleRequest = useCallback(
    (value) => {
      setRequest(value)
      setMessages(
        [
          { id: v4(), message: value.question, type: 'question', uploads: value.uploads },
          { id: v4(), message: '', type: 'answer', status: 'responding' }
        ],
        { pushToLast: true }
      )
    },
    [setMessages]
  )

  const connect = useCallback(async () => {
    if (!chatFlowId || !chatSessionId || !request.question || response.status !== 'free') return
    try {
      setResponse((prev) => ({ ...prev, status: 'waiting', chatFlowId, chatSessionId }))

      const req = {
        ...request,
        chatId: chatSessionId,
        streaming: true
      }

      const dataLogin = localStorage.getItem('dataLogin') ? JSON?.parse(localStorage.getItem('dataLogin')) : {}
      const accessToken = dataLogin?.accessToken || ''
      abortController.current = new AbortController()
      const res = await fetch(`${baseURL}/api/v1/prediction/${chatFlowId}`, {
        method: 'POST',
        body: JSON.stringify(req),
        signal: abortController.current.signal,
        headers: {
          'Content-Type': 'application/json',
          ...(accessToken && { Authorization: `Bearer ${accessToken}` })
        }
      })

      setRequest({ question: '', uploads: [] })
      const reader = res.body.getReader()
      const decoder = new TextDecoder()
      let isError = false

      while (true) {
        const { done, value } = await reader.read()

        if (done || isError) break

        const res = decoder.decode(value)
        const parts = res
          .split('\n')
          ?.filter(
            (item) =>
              item.includes('"event":"start"') ||
              item.includes('"event":"token"') ||
              item.includes('"event":"end"') ||
              item.includes('"event":"recheck"') ||
              item.includes('"event":"error"')
          )
          ?.map((item) => item.split('data:', 2)?.find(Boolean))

        if (parts?.length)
          parts.forEach((part) => {
            try {
              const res = JSON.parse(part)
              const event = res?.event
              const data = res?.data
              if (event === 'token') {
                if (response.status !== 'responding') setResponse((prev) => ({ ...prev, status: 'responding' }))
                setAnswer((prev) => prev.concat(data))
              }
              if (event === 'error') {
                console.error(data)
                throw new Error()
              }
              if (event === 'recheck' && response.status !== 'recheck') setResponse((prev) => ({ ...prev, status: 'recheck' }))
              if (event === 'start') setResponse((prev) => ({ ...prev, status: 'responding' }))
              if (event === 'end') {
                setResponse({ status: 'free', error: null })
                setAnswer('')
              }
            } catch (error) {
              setMessages({ type: 'abort', message: 'Có lỗi xảy ra trong quá trình trả lời, vui lòng thử lại sau.' }, { replaceLast: true })
              setResponse({ status: 'free', error: null })
              setAnswer('')
              isError = true
              abortController.current?.abort()
            }
          })
      }
    } catch (err) {
      if (err.name === 'AbortError') return console.info('Aborted!')
      console.error(err)
      const error = err instanceof Error ? err : new Error(String(err))
      setAnswer('')
      setResponse((prev) => ({ ...prev, error, status: 'free' }))
      setMessages({ type: 'abort', message: 'Có lỗi xảy ra trong quá trình trả lời, vui lòng thử lại sau.' }, { replaceLast: true })
    }
  }, [chatFlowId, chatSessionId, request, response])

  useEffect(() => {
    handleMessage(answer)
  }, [answer])

  useEffect(() => {
    connect()
  }, [connect])

  return {
    response,
    handleRequest
  }
}
