import { Box as MuiBox, styled, useMediaQuery, useTheme } from '@mui/material'
import { memo } from 'react'
import ChatContent from './components/ChatContent'
import ChatHeader from './components/ChatHeader'
import ChatSidebars from './components/ChatSidebars'
import { useSidebar } from './hooks/ChatHook'

const Box = styled(MuiBox, {
  shouldForwardProp: (prop) => prop !== 'open' && prop !== 'isMobile'
})(({ theme, open, isMobile }) => ({
  width: '100%',
  transition: theme.transitions.create(['margin', 'width'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen
  }),
  marginLeft: isMobile ? 0 : '-300px',
  ...(open && {
    width: isMobile ? '100%' : 'calc(100% - 300px)',
    marginLeft: 0,
    transition: theme.transitions.create(['margin', 'width'], {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen
    })
  })
}))

const Chat = () => {
  const [{ open }] = useSidebar()

  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))

  return (
    <MuiBox sx={{ display: 'flex', height: '100vh', width: '100%', overflow: 'hidden' }}>
      <ChatSidebars />
      <Box open={open} isMobile={isMobile}>
        <ChatHeader />
        <ChatContent />
      </Box>
    </MuiBox>
  )
}

export default memo(Chat)
