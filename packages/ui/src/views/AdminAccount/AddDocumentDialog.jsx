import React, { useState, useEffect, useCallback } from 'react'
import { Dialog, DialogTitle, DialogContent, IconButton, TextField, InputAdornment, Button } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import SearchIcon from '@mui/icons-material/Search'
import PropTypes from 'prop-types'
import { DataGrid } from '@mui/x-data-grid'
import { listS3Objects } from '@/api/s3'
import { useSelector } from 'react-redux'

function useS3List(search = '', displayPrefixesArray = []) {
  console.log('🚀 ~ AddDocumentDialog.jsx:11 ~ useS3List ~ displayPrefixesArray:', displayPrefixesArray)
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  const fetchData = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const res = await listS3Objects({ prefix: search })

      const data = res?.data

      if (data) {
        const parsed = [
          // folders
          ...(data.CommonPrefixes || [])
            .map((p) => ({
              key: p.Prefix,
              size: 0,
              lastModified: '',
              isFolder: true
            }))
            .filter((i) => (displayPrefixesArray?.length > 0 ? displayPrefixesArray.includes(i.key) : true))
        ]
        setItems(parsed)
      }
    } catch (e) {
      setError(e.message)
    } finally {
      setLoading(false)
    }
  }, [search])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { items, loading, error }
}

const S3FileTable = ({ items, loading, onAdd, selectPrefixesArray }) => {
  const [sel, setSel] = React.useState(selectPrefixesArray || [])
  const [pageSize, setPageSize] = React.useState(100)

  const cols = [
    {
      field: 'isFolder',
      headerName: 'Thư mục',
      width: 120,
      renderCell: (params) => (params.value ? '📁' : '')
    },
    {
      field: 'key',
      headerName: 'Tên',
      flex: 1,
      renderCell: (params) =>
        params.row.isFolder ? (
          <button style={{ color: 'blue', background: 'none', border: 'none', padding: 0 }} tabIndex={0}>
            {params.value}
          </button>
        ) : (
          params.value
        )
    }
  ]

  useEffect(() => {
    if (selectPrefixesArray?.length) {
      setSel(selectPrefixesArray)
    }
  }, [selectPrefixesArray])

  return (
    <div className='w-full' style={{ height: 'calc(100% - 130px)' }}>
      <DataGrid
        rows={items?.map((i) => ({ id: i.key, ...i }))}
        columns={cols}
        checkboxSelection
        loading={loading}
        pageSize={pageSize}
        onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
        rowsPerPageOptions={[]}
        onRowSelectionModelChange={(newSelection) => {
          const selectedIds = newSelection
          setSel(selectedIds)
        }}
        rowSelection={sel}
        rowSelectionModel={sel}
      />
      <div className='flex space-x-2 mt-2'>
        <Button variant='contained' color='primary' onClick={() => onAdd(sel)}>
          Thêm
        </Button>
      </div>
    </div>
  )
}

S3FileTable.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      size: PropTypes.number.isRequired,
      lastModified: PropTypes.string.isRequired,
      isFolder: PropTypes.bool.isRequired
    })
  ).isRequired,
  loading: PropTypes.bool.isRequired,
  onAdd: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  rootCurrent: PropTypes.string.isRequired,
  setRootCurrent: PropTypes.func.isRequired,
  selectPrefixesArray: PropTypes.arrayOf(PropTypes.string).isRequired
}

const SearchBar = ({ value, onChange }) => (
  <TextField
    size='small'
    variant='outlined'
    placeholder='Tìm kiếm thư mục'
    value={value}
    onChange={(e) => onChange(e.target.value)}
    InputProps={{
      startAdornment: (
        <InputAdornment position='start'>
          <SearchIcon />
        </InputAdornment>
      )
    }}
    className='w-full md:w-1/2'
  />
)

SearchBar.propTypes = {
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired
}

const AddDocumentDialog = ({ open, onClose, displayPrefixes, setDisplayPrefixes, selectedGroup }) => {
  const user = useSelector((state) => state.user)
  const isMasterAdmin = user?.role === 'MASTER_ADMIN'
  const isSiteAdmin = user?.role === 'SITE_ADMIN'
  // const isGroupAdmin = user?.role === 'ADMIN'

  const [rootCurrent, setRootCurrent] = useState('')
  const [filter, setFilterItem] = useState([])
  const [search, setSearch] = React.useState('')

  const displayPrefixesGroup = selectedGroup?.displayPrefixes ? JSON?.parse(selectedGroup?.displayPrefixes.replace(/'/g, '"')) || [] : ''
  const displayPrefixesArray = isMasterAdmin || isSiteAdmin ? [] : displayPrefixesGroup
  const selectPrefixesArray = JSON?.parse(displayPrefixes.replace(/'/g, '"')) || []

  const { items, loading, error } = useS3List(rootCurrent, displayPrefixesArray)

  useEffect(() => {
    const filteredItems = items.filter((item) => item.key.toLowerCase().includes(search.toLowerCase().trim()))
    setFilterItem(filteredItems)
  }, [search, items])

  const handleAdd = async (keys) => {
    try {
      setDisplayPrefixes(JSON.stringify(keys))
      onClose()
    } catch (error) {
      console.log('🚀 ~ AddDocumentDialog.jsx:195 ~ handleAdd ~ error:', error)
    }
  }

  return (
    <Dialog open={open} fullWidth maxWidth='lg'>
      <DialogTitle className='flex justify-between items-center'>
        Thêm Tài liệu được truy cập
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent className='space-y-4 h-[70vh]'>
        <SearchBar value={search} onChange={setSearch} />
        {error && <div className='text-red-500'>{error}</div>}
        <div className='flex items-center space-x-2'>
          {/* <button className='text-blue-500 hover:underline' onClick={() => setRootCurrent('')}>
            Gốc
          </button> */}
          {rootCurrent &&
            rootCurrent
              .split('/')
              .filter(Boolean)
              .map((folder, index, arr) => (
                <React.Fragment key={index}>
                  <span>/</span>
                  <button
                    className='text-blue-500 hover:underline'
                    onClick={() => {
                      const newPath = arr.slice(0, index + 1).join('/') + '/'
                      setRootCurrent(newPath)
                    }}
                  >
                    {folder}
                  </button>
                </React.Fragment>
              ))}
        </div>

        {!(isMasterAdmin || isSiteAdmin) && (!displayPrefixesArray || displayPrefixesArray?.length === 0) ? (
          <div className='flex items-center justify-center h-full'>Không có tài liệu nào được gán</div>
        ) : (
          <S3FileTable
            rootCurrent={rootCurrent}
            setRootCurrent={setRootCurrent}
            items={filter}
            loading={loading}
            onAdd={handleAdd}
            selectPrefixesArray={selectPrefixesArray}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}

AddDocumentDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  displayPrefixes: PropTypes.arrayOf(PropTypes.string).isRequired,
  setDisplayPrefixes: PropTypes.func.isRequired,
  selectedGroup: PropTypes.object
}

export default AddDocumentDialog
