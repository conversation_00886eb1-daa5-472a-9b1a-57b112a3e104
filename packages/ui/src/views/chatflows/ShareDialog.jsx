import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Box,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Checkbox,
  Chip,
  IconButton,
  Button,
  Typography,
  Paper,
  CircularProgress
} from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { IconSearch, IconUserCircle, IconUsers, IconX, IconTrash } from '@tabler/icons-react'
import { getUsers, getAllGroupUsers } from '@/api/user'

// Styled components for cleaner code
const SearchBox = ({ placeholder, value, onChange }) => (
  <TextField
    fullWidth
    placeholder={placeholder}
    variant='outlined'
    size='small'
    value={value}
    onChange={onChange}
    sx={{ mb: 2 }}
    InputProps={{
      startAdornment: (
        <InputAdornment position='start'>
          <IconSearch size={20} />
        </InputAdornment>
      )
    }}
  />
)

const EntityItem = ({ entity, selected, onToggle, onRemove, isShared }) => {
  const theme = useTheme()

  return (
    <ListItem
      sx={{
        mb: 1,
        borderRadius: 1,
        bgcolor: selected ? `${theme.palette.primary.light}30` : 'background.paper',
        '&:hover': {
          bgcolor: selected ? `${theme.palette.primary.light}50` : 'action.hover'
        }
      }}
    >
      {!isShared && (
        <ListItemIcon>
          <Checkbox edge='start' checked={selected} onChange={() => onToggle(entity)} color='primary' />
        </ListItemIcon>
      )}
      <ListItemIcon>
        {entity.type === 'user' ? (
          <IconUserCircle size={24} color={theme.palette.primary.main} />
        ) : (
          <IconUsers size={24} color={theme.palette.secondary.main} />
        )}
      </ListItemIcon>
      <ListItemText
        primary={entity.type === 'user' ? entity.username : entity.groupname}
        secondary={entity.email || entity.description || `${entity.members || 0} thành viên`}
      />
      {isShared && (
        <ListItemSecondaryAction>
          <IconButton edge='end' onClick={() => onRemove(entity)}>
            <IconTrash size={20} color={theme.palette.error.main} />
          </IconButton>
        </ListItemSecondaryAction>
      )}
    </ListItem>
  )
}

const ShareDialog = ({ open, onClose, onSubmit }) => {
  const theme = useTheme()
  const [tabValue, setTabValue] = useState(0)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedEntities, setSelectedEntities] = useState([])
  const [sharedUsers, setSharedUsers] = useState([])
  const [sharedGroups, setSharedGroups] = useState([])
  const [usersLoading, setUsersLoading] = useState(false)
  const [groupsLoading, setGroupsLoading] = useState(false)

  // Mock data - in a real app, these would come from API calls
  const [users, setUsers] = useState([])

  const [groups, setGroups] = useState([])

  useEffect(() => {
    setSearchQuery('')
  }, [tabValue])

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue)
  }

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value)
  }

  const toggleEntitySelection = (entity) => {
    if (selectedEntities.some((e) => e.id === entity.id && e.type === entity.type)) {
      setSelectedEntities(selectedEntities.filter((e) => !(e.id === entity.id && e.type === entity.type)))
    } else {
      setSelectedEntities([...selectedEntities, entity])
    }
  }

  const removeSharedEntity = (entity) => {
    if (entity.type === 'user') {
      setSharedUsers(sharedUsers.filter((u) => u.id !== entity.id))
    } else {
      setSharedGroups(sharedGroups.filter((g) => g.id !== entity.id))
    }
  }

  const handleSubmit = () => {
    // Add selected entities to shared lists
    const newUsers = selectedEntities.filter((e) => e.type === 'user')
    const newGroups = selectedEntities.filter((e) => e.type === 'group')

    setSharedUsers([...sharedUsers, ...newUsers])
    setSharedGroups([...sharedGroups, ...newGroups])
    setSelectedEntities([])

    // Call the onSubmit prop with all shared entities
    onSubmit({
      users: [...sharedUsers, ...newUsers],
      groups: [...sharedGroups, ...newGroups]
    })
  }

  const isEntitySelected = (entity) => selectedEntities.some((e) => e.id === entity.id && e.type === entity.type)

  useEffect(() => {
    const fetchUsers = async () => {
      setUsersLoading(true)
      try {
        const response = await getUsers(10, searchQuery)
        response.data = response.data.map((user) => ({
          ...user,
          type: 'user'
        }))
        setUsers(response.data)
      } catch (error) {
        console.error('Error fetching users:', error)
      } finally {
        setUsersLoading(false)
      }
    }

    const fetchGroups = async () => {
      setGroupsLoading(true)
      try {
        const response = await getAllGroupUsers(searchQuery)
        response.data = response.data.map((group) => ({
          ...group,
          members: group.users.length || 0,
          type: 'group'
        }))
        setGroups(response.data)
      } catch (error) {
        console.error('Error fetching groups:', error)
      } finally {
        setGroupsLoading(false)
      }
    }

    if (tabValue === 1) {
      fetchUsers()
    } else if (tabValue === 0) {
      fetchGroups()
    }
  }, [searchQuery, tabValue])

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='md'
      fullWidth
      PaperProps={{
        elevation: 3,
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display='flex' justifyContent='space-between' alignItems='center'>
          <Typography variant='h4'>Chia sẻ</Typography>
          <IconButton edge='end' color='inherit' onClick={onClose} aria-label='close'>
            <IconX />
          </IconButton>
        </Box>
      </DialogTitle>

      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        indicatorColor='primary'
        textColor='primary'
        variant='fullWidth'
        sx={{ px: 3, borderBottom: 1, borderColor: 'divider' }}
      >
        <Tab
          label={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconUsers size={20} />
              <span>Chia sẻ cho Nhóm</span>
            </Box>
          }
          id='share-tab-0'
          aria-controls='share-tabpanel-0'
        />
        <Tab
          label={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconUserCircle size={20} />
              <span>Chia sẻ cho Người dùng</span>
            </Box>
          }
          id='share-tab-1'
          aria-controls='share-tabpanel-1'
        />
      </Tabs>

      <DialogContent dividers sx={{ p: 3 }}>
        <Box role='tabpanel' hidden={tabValue !== 0} id='share-tabpanel-0' aria-labelledby='share-tab-0'>
          {tabValue === 0 && (
            <Box>
              <SearchBox placeholder='Tìm kiếm nhóm...' value={searchQuery} onChange={handleSearchChange} />

              {sharedGroups.length > 0 && (
                <Box mb={3}>
                  <Typography variant='subtitle1' fontWeight='bold' mb={1}>
                    Đã chia sẻ với
                  </Typography>
                  <Paper variant='outlined' sx={{ p: 1, maxHeight: 200, overflow: 'auto' }}>
                    <List disablePadding>
                      {sharedGroups.map((group) => (
                        <EntityItem key={group.id} entity={group} onRemove={removeSharedEntity} isShared />
                      ))}
                    </List>
                  </Paper>
                </Box>
              )}

              <Box mb={2}>
                <Typography variant='subtitle1' fontWeight='bold' mb={1}>
                  Kết quả tìm kiếm
                </Typography>
                <Paper variant='outlined' sx={{ p: 1, maxHeight: 200, overflow: 'auto' }}>
                  {groupsLoading ? (
                    <Box display='flex' justifyContent='center' alignItems='center' py={4}>
                      <CircularProgress size={40} />
                    </Box>
                  ) : groups.length > 0 ? (
                    <List disablePadding>
                      {groups.map((group) => (
                        <EntityItem
                          key={group.id}
                          entity={group}
                          selected={isEntitySelected(group)}
                          onToggle={toggleEntitySelection}
                          isShared={false}
                        />
                      ))}
                    </List>
                  ) : (
                    <Box display='flex' justifyContent='center' alignItems='center' py={3}>
                      <Typography color='textSecondary'>Không tìm thấy kết quả</Typography>
                    </Box>
                  )}
                </Paper>
              </Box>
            </Box>
          )}
        </Box>

        <Box role='tabpanel' hidden={tabValue !== 1} id='share-tabpanel-1' aria-labelledby='share-tab-1'>
          {tabValue === 1 && (
            <Box>
              <SearchBox placeholder='Tìm kiếm người dùng...' value={searchQuery} onChange={handleSearchChange} />

              {sharedUsers.length > 0 && (
                <Box mb={3}>
                  <Typography variant='subtitle1' fontWeight='bold' mb={1}>
                    Đã chia sẻ với
                  </Typography>
                  <Paper variant='outlined' sx={{ p: 1, maxHeight: 200, overflow: 'auto' }}>
                    <List disablePadding>
                      {sharedUsers.map((user) => (
                        <EntityItem key={user.id} entity={user} onRemove={removeSharedEntity} isShared />
                      ))}
                    </List>
                  </Paper>
                </Box>
              )}

              <Box mb={2}>
                <Typography variant='subtitle1' fontWeight='bold' mb={1}>
                  Kết quả tìm kiếm
                </Typography>
                <Paper variant='outlined' sx={{ p: 1, maxHeight: 200, overflow: 'auto' }}>
                  {usersLoading ? (
                    <Box display='flex' justifyContent='center' alignItems='center' py={4}>
                      <CircularProgress size={40} />
                    </Box>
                  ) : users.length > 0 ? (
                    <List disablePadding>
                      {users.map((user) => (
                        <EntityItem
                          key={user.id}
                          entity={user}
                          selected={isEntitySelected(user)}
                          onToggle={toggleEntitySelection}
                          isShared={false}
                        />
                      ))}
                    </List>
                  ) : (
                    <Box display='flex' justifyContent='center' alignItems='center' py={3}>
                      <Typography color='textSecondary'>Không tìm thấy kết quả</Typography>
                    </Box>
                  )}
                </Paper>
              </Box>
            </Box>
          )}
        </Box>

        {selectedEntities.length > 0 && (
          <Box mt={3}>
            <Typography variant='subtitle1' fontWeight='bold' mb={1}>
              Đã chọn ({selectedEntities.length})
            </Typography>
            <Box display='flex' flexWrap='wrap' gap={1} p={2} bgcolor={theme.palette.background.neutral || '#f5f5f5'} borderRadius={1}>
              {selectedEntities.map((entity) => (
                <Chip
                  key={`${entity.type}-${entity.id}`}
                  label={entity.username || entity.groupname}
                  icon={entity.type === 'user' ? <IconUserCircle size={16} /> : <IconUsers size={16} />}
                  onDelete={() => toggleEntitySelection(entity)}
                  color='primary'
                  variant='outlined'
                />
              ))}
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
        <Button onClick={onClose} color='inherit'>
          Hủy
        </Button>
        <Button onClick={handleSubmit} variant='contained' color='primary' disabled={selectedEntities.length === 0}>
          Áp dụng chia sẻ
        </Button>
      </DialogActions>
    </Dialog>
  )
}

ShareDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired
}

SearchBox.propTypes = {
  placeholder: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired
}

EntityItem.propTypes = {
  entity: PropTypes.object.isRequired,
  selected: PropTypes.bool,
  onToggle: PropTypes.func,
  onRemove: PropTypes.func,
  isShared: PropTypes.bool
}

export default ShareDialog
