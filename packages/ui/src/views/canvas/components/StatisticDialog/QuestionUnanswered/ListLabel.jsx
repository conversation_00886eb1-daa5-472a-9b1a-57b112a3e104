import faqApi from '@/api/faq'
import { closeSnackbar, enqueueSnackbar } from '@/store/actions'
import ComponentLoader from '@/ui-component/loading/ComponentLoader'
import useNotifier from '@/utils/useNotifier'
import { LoadingButton } from '@mui/lab'
import {
  AppBar,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  OutlinedInput,
  Paper,
  Popover,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Toolbar,
  Tooltip,
  Typography
} from '@mui/material'
import { IconCheck, IconPencil, IconPlus, IconTrashXFilled, IconX } from '@tabler/icons-react'
import clsx from 'clsx'
import PropTypes from 'prop-types'
import { Fragment, memo, useCallback, useEffect, useMemo, useState } from 'react'
import { createPortal } from 'react-dom'
import { useDispatch } from 'react-redux'

const TH = ['STT', 'Tên', 'Giá trị', 'add']

const DefaultForm = { id: '', label: '', isUsing: false, isAdd: false }

const ListLabel = ({ flowId, open, onClose, onRefreshChart }) => {
  useNotifier()
  const dispatch = useDispatch()

  const dispatchEnqueueSnackbar = (...args) => dispatch(enqueueSnackbar(...args))
  const dispatchCloseSnackbar = (...args) => dispatch(closeSnackbar(...args))

  const [loadingTable, setLoadingTable] = useState(false)
  const [loadingForm, setLoadingForm] = useState(false)
  const [loadingDelete, setLoadingDelete] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const [labels, setLabels] = useState([])
  const [form, setForm] = useState(DefaultForm)

  const handleAnchorEl = (event, label) => {
    setAnchorEl(event?.currentTarget || null)
    if (event && label) setForm((prev) => ({ ...prev, id: label.id }))
    else setForm(DefaultForm)
  }

  const handleCreateOrUpdateLabel = async () => {
    if (!form.label) return
    setLoadingForm(true)

    const req = {
      chatflowId: flowId,
      label: form.label
    }
    if (form.id) req.id = form.id

    const res = await faqApi.createOrUpdateLabel(req)
    if (res.data?.label?.id) {
      dispatchEnqueueSnackbar({
        message: req.id ? 'Sửa nhãn thành công' : 'Thêm nhãn thành công',
        options: {
          variant: 'success',
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => dispatchCloseSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
      refreshData()
    }
    setLoadingForm(false)
  }

  const handleClose = () => {
    setForm(DefaultForm)
    onClose()
  }

  const handleDeleteLabel = async () => {
    setLoadingDelete(true)
    const res = await faqApi.deleteLabel(form.id, flowId)
    if (res.data) {
      dispatchEnqueueSnackbar({
        message: 'Xóa nhãn thành công',
        options: {
          variant: 'success',
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => dispatchCloseSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
      refreshData()
    }
    setLoadingDelete(false)
  }

  const handleGetListLabel = useCallback(async () => {
    if (!flowId) return
    setLoadingTable(true)
    const res = await faqApi.getListClassifyQA(flowId)
    if (Array.isArray(res.data?.data)) setLabels(res.data.data)
    setLoadingTable(false)
  }, [flowId])

  const refreshData = useCallback(() => {
    handleGetListLabel()
    onRefreshChart()
    setForm(DefaultForm)
    setAnchorEl(null)
  }, [handleGetListLabel, onRefreshChart])

  const ViewForm = useMemo(() => {
    return (
      <TableRow key={0}>
        <TableCell className='min-w-[50px] max-w-[50px] text-center'></TableCell>
        <TableCell className='min-w-[350px] max-w-[350px]'>
          <OutlinedInput
            required
            size='small'
            value={form.label}
            onChange={(e) => setForm((prev) => ({ ...prev, label: e.target.value }))}
            className='w-full'
          />
        </TableCell>
        <TableCell className='min-w-[200px] max-w-[200px]'>0</TableCell>
        <TableCell className='min-w-[80px] max-w-[80px]'>
          <Stack direction='row' justifyContent='center' alignItems='center' gap={2}>
            {loadingForm ? (
              <CircularProgress size={20} />
            ) : (
              <>
                <Tooltip title='Hủy' placement='top'>
                  <IconX className='text-red-500 cursor-pointer' onClick={() => setForm(DefaultForm)} />
                </Tooltip>
                <Tooltip title='Lưu' placement='top'>
                  <IconCheck className='text-green-500 cursor-pointer' onClick={handleCreateOrUpdateLabel} />
                </Tooltip>
              </>
            )}
          </Stack>
        </TableCell>
      </TableRow>
    )
  }, [form, loadingForm])

  useEffect(() => {
    handleGetListLabel()
  }, [handleGetListLabel])

  return createPortal(
    <Fragment>
      <Dialog
        open={open}
        fullWidth
        maxWidth='md'
        sx={{
          '& .MuiDialog-container': {
            alignItems: 'flex-start'
          }
        }}
      >
        <AppBar sx={{ position: 'relative', background: '#2196f3' }}>
          <Toolbar sx={{ padding: '8px 16px' }}>
            <Typography variant='h3' component='div' color='white' sx={{ flex: 1 }}>
              Danh sách nhãn
            </Typography>
            <IconButton edge='end' color='inherit' onClick={handleClose}>
              <IconX />
            </IconButton>
          </Toolbar>
        </AppBar>
        <DialogContent className='bg-slate-100 relative' sx={{ padding: '16px' }}>
          <Paper className='relative min-h-[200px]'>
            <TableContainer>
              <Table size='small'>
                <TableHead>
                  <TableRow className='bg-slate-100'>
                    {TH.map((th) => {
                      if (th === 'add')
                        return (
                          <TableCell key={th} className='whitespace-nowrap'>
                            <Stack direction='row' justifyContent='center' alignItems='center'>
                              <Tooltip title='Thêm mới' placement='top'>
                                <IconPlus
                                  onClick={() => !form.isUsing && setForm((prev) => ({ ...prev, isUsing: true, isAdd: true }))}
                                  className={clsx('text-[#2196f3]', form.isUsing ? 'cursor-not-allowed opacity-50' : 'cursor-pointer')}
                                />
                              </Tooltip>
                            </Stack>
                          </TableCell>
                        )
                      return (
                        <TableCell key={th} className='whitespace-nowrap'>
                          {th}
                        </TableCell>
                      )
                    })}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {labels.length ? (
                    labels.map((label, index) => {
                      if (form.id === label.id && form.isUsing) return ViewForm
                      return (
                        <Fragment key={label.id}>
                          {index === 0 && form.isUsing && form.isAdd ? ViewForm : null}
                          <TableRow className={clsx(form.id === label.id ? 'bg-[#ddeffd]' : 'bg-white')}>
                            <TableCell className='min-w-[50px] max-w-[50px] text-center'>{index + 1}</TableCell>
                            <TableCell className='min-w-[350px] max-w-[350px]'>{label.category}</TableCell>
                            <TableCell className='min-w-[200px] max-w-[200px]'>{label.total}</TableCell>
                            <TableCell className='min-w-[80px] max-w-[80px]'>
                              <Stack direction='row' justifyContent='center' alignItems='center' gap={2}>
                                <Tooltip title='Sửa' placement='top'>
                                  <IconPencil
                                    className={clsx(form.isUsing ? 'cursor-not-allowed opacity-50' : 'cursor-pointer')}
                                    onClick={() => !form.isUsing && setForm({ ...label, label: label.category, isUsing: true, isAdd: false})}
                                  />
                                </Tooltip>
                                <Tooltip title='Xóa' placement='top'>
                                  <IconTrashXFilled
                                    className={clsx('text-red-500', form.isUsing ? 'cursor-not-allowed opacity-50' : 'cursor-pointer')}
                                    onClick={(e) => !form.isUsing && handleAnchorEl(e, label)}
                                  />
                                </Tooltip>
                              </Stack>
                            </TableCell>
                          </TableRow>
                        </Fragment>
                      )
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={TH.length} className='text-center'>
                        {form.isUsing && form.isAdd 
                          ? ViewForm 
                          : "Không có dữ liệu"}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
          <ComponentLoader loading={loadingTable} />
        </DialogContent>
      </Dialog>
      <Popover
        id={form.id}
        open={!!form.id && !form.isUsing}
        anchorEl={anchorEl}
        onClose={() => handleAnchorEl()}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left'
        }}
      >
        <Paper sx={{ p: 2 }}>
          <Stack gap={1}>
            <Typography className='text-sm font-semibold'>Xóa nhãn</Typography>
            <Typography className='text-sm'>Bạn có chắc muốn xóa nhãn không?</Typography>
            <Stack direction='row' justifyContent='flex-end' gap={1}>
              <Button variant='outlined' color='inherit' size='small' onClick={() => handleAnchorEl()}>
                Hủy
              </Button>
              <LoadingButton variant='contained' size='small' color='primary' loading={loadingDelete} onClick={handleDeleteLabel}>
                Xóa
              </LoadingButton>
            </Stack>
          </Stack>
        </Paper>
      </Popover>
    </Fragment>,
    document.body
  )
}

ListLabel.propTypes = {
  flowId: PropTypes.string,
  open: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
  onRefreshChart: PropTypes.func
}

export default memo(ListLabel)
