import React from 'react'
import { FormControl, Select, MenuItem, Box, Typography, ListItemIcon, ListItemText } from '@mui/material'
import FlagIcon from './FlagIcon'
import { languageConfig, generateLanguageOptions } from '../config/languages'

/**
 * Enhanced Language Selector with SVG flag support
 * Provides consistent flag display across all browsers
 */
const LanguageSelector = ({
  value,
  onChange,
  includeDetectLanguage = false,
  t = null,
  size = 'medium',
  variant = 'outlined',
  fullWidth = false,
  disabled = false,
  useCdnFlags = false,
  showFlagOnly = false,
  showNativeName = true,
  ...props
}) => {
  const options = generateLanguageOptions(includeDetectLanguage, t)

  // Find current language config
  const currentLang = languageConfig.find((lang) => lang.name === value) || options.find((opt) => opt.label === value)

  const renderValue = (selected) => {
    const selectedOption = options.find((opt) => opt.label === selected)
    if (!selectedOption) return selected

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {selectedOption.flagCode ? (
          <FlagIcon
            countryCode={selectedOption.code === 'detect' ? null : selectedOption.code}
            size={size === 'small' ? 16 : 20}
            useCdn={useCdnFlags}
            fallbackToEmoji={!selectedOption.flagCode}
          />
        ) : (
          <Box sx={{ fontSize: size === 'small' ? '16px' : '20px' }}>{selectedOption.flagEmoji}</Box>
        )}
        {!showFlagOnly && (
          <Typography variant='body2' component='span'>
            {showNativeName ? selectedOption.label : selectedOption.englishName}
          </Typography>
        )}
      </Box>
    )
  }

  return (
    <FormControl size={size} variant={variant} fullWidth={fullWidth} disabled={disabled} {...props}>
      <Select
        value={value}
        onChange={onChange}
        renderValue={renderValue}
        sx={{
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center'
          }
        }}
      >
        {options.map((option) => (
          <MenuItem key={option.code} value={option.label}>
            <ListItemIcon sx={{ minWidth: 'auto', mr: 1 }}>
              {option.flagCode ? (
                <FlagIcon
                  countryCode={option.code === 'detect' ? null : option.code}
                  size={size === 'small' ? 16 : 20}
                  useCdn={useCdnFlags}
                  fallbackToEmoji={!option.flagCode}
                />
              ) : (
                <Box sx={{ fontSize: size === 'small' ? '16px' : '20px' }}>{option.flagEmoji}</Box>
              )}
            </ListItemIcon>
            <ListItemText>
              <Box>
                <Typography variant='body2' component='div'>
                  {showNativeName ? option.label : option.englishName}
                </Typography>
                {showNativeName && option.englishName !== option.label && (
                  <Typography variant='caption' color='text.secondary' component='div'>
                    {option.englishName}
                  </Typography>
                )}
              </Box>
            </ListItemText>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}

export default LanguageSelector

// Export simplified flag-only selector
export const FlagOnlySelector = (props) => <LanguageSelector {...props} showFlagOnly={true} size='small' />

// Export compact selector with English names
export const CompactLanguageSelector = (props) => <LanguageSelector {...props} showNativeName={false} size='small' />
