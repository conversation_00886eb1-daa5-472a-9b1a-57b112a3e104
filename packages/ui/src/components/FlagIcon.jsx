import React from 'react'
import { Box } from '@mui/material'

/**
 * FlagIcon component that displays SVG country flags
 * Provides fallback for emoji flags that don't render properly on some browsers/OS
 */

// SVG flag data for supported countries
const flagSvgData = {
  vi: {
    // Vietnam flag SVG
    svg: (
      <svg viewBox='0 0 30 20' xmlns='http://www.w3.org/2000/svg'>
        <rect width='30' height='20' fill='#da251d' />
        <polygon
          points='15,4 16.18,8.82 21,8.82 17.41,11.59 18.59,16.41 15,13.64 11.41,16.41 12.59,11.59 9,8.82 13.82,8.82'
          fill='#ffff00'
        />
      </svg>
    ),
    name: 'Vietnam'
  },
  en: {
    // USA flag SVG (simplified)
    svg: (
      <svg viewBox='0 0 30 20' xmlns='http://www.w3.org/2000/svg'>
        <rect width='30' height='20' fill='#b22234' />
        <rect width='30' height='1.54' y='1.54' fill='#ffffff' />
        <rect width='30' height='1.54' y='4.62' fill='#ffffff' />
        <rect width='30' height='1.54' y='7.69' fill='#ffffff' />
        <rect width='30' height='1.54' y='10.77' fill='#ffffff' />
        <rect width='30' height='1.54' y='13.85' fill='#ffffff' />
        <rect width='30' height='1.54' y='16.92' fill='#ffffff' />W
        <rect width='12' height='10.77' fill='#3c3b6e' />
      </svg>
    ),
    name: 'United States'
  },
  ja: {
    // Japan flag SVG
    svg: (
      <svg viewBox='0 0 30 20' xmlns='http://www.w3.org/2000/svg'>
        <rect width='30' height='20' fill='#ffffff' />
        <circle cx='15' cy='10' r='6' fill='#bc002d' />
      </svg>
    ),
    name: 'Japan'
  },
  zh: {
    // China flag SVG
    svg: (
      <svg viewBox='0 0 30 20' xmlns='http://www.w3.org/2000/svg'>
        <rect width='30' height='20' fill='#de2910' />
        <polygon points='6,3 7.09,6.18 10.5,6.18 7.91,8.32 8.91,11.5 6,9.36 3.09,11.5 4.09,8.32 1.5,6.18 4.91,6.18' fill='#ffde00' />
        <polygon points='12,2 12.5,3.5 14,3.5 12.75,4.5 13.25,6 12,5 10.75,6 11.25,4.5 10,3.5 11.5,3.5' fill='#ffde00' />
        <polygon points='14,5 14.5,6.5 16,6.5 14.75,7.5 15.25,9 14,8 12.75,9 13.25,7.5 12,6.5 13.5,6.5' fill='#ffde00' />
        <polygon points='14,9 14.5,10.5 16,10.5 14.75,11.5 15.25,13 14,12 12.75,13 13.25,11.5 12,10.5 13.5,10.5' fill='#ffde00' />
        <polygon points='12,12 12.5,13.5 14,13.5 12.75,14.5 13.25,16 12,15 10.75,16 11.25,14.5 10,13.5 11.5,13.5' fill='#ffde00' />
      </svg>
    ),
    name: 'China'
  }
}

// CDN URLs for flag icons as fallback
const flagCdnUrls = {
  vi: 'https://flagicons.lipis.dev/flags/4x3/vn.svg',
  en: 'https://flagicons.lipis.dev/flags/4x3/us.svg',
  ja: 'https://flagicons.lipis.dev/flags/4x3/jp.svg',
  zh: 'https://flagicons.lipis.dev/flags/4x3/cn.svg'
}

// Alternative CDN for backup
const flagCdnUrlsBackup = {
  vi: 'https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/VN.svg',
  en: 'https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/US.svg',
  ja: 'https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/JP.svg',
  zh: 'https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CN.svg'
}

const FlagIcon = ({ countryCode, size = 20, style = {}, fallbackToEmoji = false, useCdn = false, ...props }) => {
  const flagData = flagSvgData[countryCode]

  if (!flagData && !useCdn) {
    // Fallback to emoji if no SVG data and not using CDN
    const emojiFlags = {
      vi: '🇻🇳',
      en: '🇺🇸',
      ja: '🇯🇵',
      zh: '🇨🇳'
    }

    if (fallbackToEmoji && emojiFlags[countryCode]) {
      return (
        <Box
          component='span'
          sx={{
            fontSize: `${size}px`,
            lineHeight: 1,
            display: 'inline-block',
            ...style
          }}
          {...props}
        >
          {emojiFlags[countryCode]}
        </Box>
      )
    }

    return null
  }

  if (useCdn) {
    // Use CDN URL for flag
    const cdnUrl = flagCdnUrls[countryCode]
    if (!cdnUrl) return null

    return (
      <Box
        component='img'
        src={cdnUrl}
        alt={`${countryCode} flag`}
        sx={{
          width: `${size}px`,
          height: `${size * 0.67}px`, // 3:2 aspect ratio
          objectFit: 'cover',
          borderRadius: '2px',
          display: 'inline-block',
          verticalAlign: 'middle',
          ...style
        }}
        onError={(e) => {
          // Fallback to backup CDN
          const backupUrl = flagCdnUrlsBackup[countryCode]
          if (backupUrl && e.target.src !== backupUrl) {
            e.target.src = backupUrl
          }
        }}
        {...props}
      />
    )
  }

  // Use inline SVG
  return (
    <Box
      component='span'
      sx={{
        width: `${size}px`,
        height: `${size * 0.67}px`, // 3:2 aspect ratio
        display: 'inline-block',
        verticalAlign: 'middle',
        '& svg': {
          width: '100%',
          height: '100%',
          borderRadius: '2px'
        },
        ...style
      }}
      {...props}
    >
      {flagData.svg}
    </Box>
  )
}

export default FlagIcon

// Export utility functions
export const getSupportedCountries = () => Object.keys(flagSvgData)

export const getFlagName = (countryCode) => flagSvgData[countryCode]?.name || countryCode

export const isFlagSupported = (countryCode) => !!flagSvgData[countryCode]

// Export language configuration with SVG flags
export const languageConfigWithFlags = [
  {
    code: 'vi',
    name: 'Tiếng Việt',
    flag: <FlagIcon countryCode='vi' size={16} />,
    flagEmoji: '🇻🇳'
  },
  {
    code: 'en',
    name: 'English',
    flag: <FlagIcon countryCode='en' size={16} />,
    flagEmoji: '🇺🇸'
  },
  {
    code: 'ja',
    name: '日本語',
    flag: <FlagIcon countryCode='ja' size={16} />,
    flagEmoji: '🇯🇵'
  },
  {
    code: 'zh',
    name: '中文',
    flag: <FlagIcon countryCode='zh' size={16} />,
    flagEmoji: '🇨🇳'
  }
]
