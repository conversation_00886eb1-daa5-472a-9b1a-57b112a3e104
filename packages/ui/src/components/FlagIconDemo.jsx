import React, { useState } from 'react'
import { 
  Box, 
  Typography, 
  Paper, 
  Stack, 
  Button, 
  ToggleButtonGroup, 
  ToggleButton,
  Chip,
  Alert
} from '@mui/material'
import FlagIcon, { 
  languageConfigWithFlags, 
  getSupportedCountries, 
  getFlagName, 
  isFlagSupported 
} from './FlagIcon'

/**
 * Demo component to showcase FlagIcon functionality
 * Shows different usage modes and fallback options
 */
const FlagIconDemo = () => {
  const [mode, setMode] = useState('inline')
  const [size, setSize] = useState(24)

  const handleModeChange = (event, newMode) => {
    if (newMode !== null) {
      setMode(newMode)
    }
  }

  const countries = getSupportedCountries()

  return (
    <Box sx={{ p: 4, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        🏴 FlagIcon Component Demo
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        This component solves the problem of flag emojis not rendering consistently across different browsers and operating systems.
      </Alert>

      {/* Mode Selection */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Display Mode
        </Typography>
        <ToggleButtonGroup
          value={mode}
          exclusive
          onChange={handleModeChange}
          aria-label="flag display mode"
        >
          <ToggleButton value="inline" aria-label="inline svg">
            Inline SVG
          </ToggleButton>
          <ToggleButton value="cdn" aria-label="cdn">
            CDN Images
          </ToggleButton>
          <ToggleButton value="emoji" aria-label="emoji fallback">
            Emoji Fallback
          </ToggleButton>
        </ToggleButtonGroup>
        
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {mode === 'inline' && 'Uses embedded SVG data for maximum compatibility'}
            {mode === 'cdn' && 'Loads flag images from external CDN (requires internet)'}
            {mode === 'emoji' && 'Falls back to emoji flags (may not render on all systems)'}
          </Typography>
        </Box>
      </Paper>

      {/* Size Control */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Size Control
        </Typography>
        <Stack direction="row" spacing={2} alignItems="center">
          {[16, 20, 24, 32, 48].map(s => (
            <Button
              key={s}
              variant={size === s ? 'contained' : 'outlined'}
              size="small"
              onClick={() => setSize(s)}
            >
              {s}px
            </Button>
          ))}
        </Stack>
      </Paper>

      {/* Flag Display */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Supported Flags
        </Typography>
        <Stack direction="row" spacing={3} flexWrap="wrap" useFlexGap>
          {countries.map(countryCode => (
            <Box key={countryCode} sx={{ textAlign: 'center', minWidth: 80 }}>
              <Box sx={{ mb: 1 }}>
                <FlagIcon 
                  countryCode={countryCode}
                  size={size}
                  useCdn={mode === 'cdn'}
                  fallbackToEmoji={mode === 'emoji'}
                />
              </Box>
              <Typography variant="caption" display="block">
                {countryCode.toUpperCase()}
              </Typography>
              <Typography variant="caption" color="text.secondary" display="block">
                {getFlagName(countryCode)}
              </Typography>
            </Box>
          ))}
        </Stack>
      </Paper>

      {/* Language Configuration Example */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Language Configuration Usage
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Example of how to use flags in language selection:
        </Typography>
        <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
          {languageConfigWithFlags.map(lang => (
            <Chip
              key={lang.code}
              icon={
                <FlagIcon 
                  countryCode={lang.code}
                  size={16}
                  useCdn={mode === 'cdn'}
                  fallbackToEmoji={mode === 'emoji'}
                />
              }
              label={lang.name}
              variant="outlined"
              sx={{ 
                '& .MuiChip-icon': { 
                  marginLeft: '8px',
                  marginRight: '-4px'
                }
              }}
            />
          ))}
        </Stack>
      </Paper>

      {/* Comparison with Emoji */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Emoji vs SVG Comparison
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Compare how emoji flags vs SVG flags render on your system:
        </Typography>
        
        <Stack spacing={2}>
          {languageConfigWithFlags.map(lang => (
            <Box key={lang.code} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ minWidth: 100 }}>
                <Typography variant="body2">{lang.name}:</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2">Emoji:</Typography>
                <Box sx={{ fontSize: '20px' }}>{lang.flagEmoji}</Box>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2">SVG:</Typography>
                <FlagIcon countryCode={lang.code} size={20} />
              </Box>
            </Box>
          ))}
        </Stack>
      </Paper>

      {/* Usage Examples */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Usage Examples
        </Typography>
        
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Basic Usage:
          </Typography>
          <Box component="pre" sx={{ 
            backgroundColor: '#f5f5f5', 
            p: 2, 
            borderRadius: 1,
            fontSize: '0.875rem',
            overflow: 'auto'
          }}>
{`import FlagIcon from './components/FlagIcon'

// Basic flag
<FlagIcon countryCode="vi" size={20} />

// With CDN
<FlagIcon countryCode="en" size={24} useCdn />

// With emoji fallback
<FlagIcon countryCode="ja" fallbackToEmoji />

// In language selector
{languageConfigWithFlags.map(lang => (
  <Box key={lang.code}>
    <FlagIcon countryCode={lang.code} size={16} />
    {lang.name}
  </Box>
))}`}
          </Box>
        </Box>

        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Available Props:
          </Typography>
          <Typography variant="body2" component="div">
            <ul>
              <li><code>countryCode</code>: 'vi' | 'en' | 'ja' | 'zh'</li>
              <li><code>size</code>: number (default: 20)</li>
              <li><code>useCdn</code>: boolean (default: false)</li>
              <li><code>fallbackToEmoji</code>: boolean (default: false)</li>
              <li><code>style</code>: CSS styles object</li>
            </ul>
          </Typography>
        </Box>
      </Paper>
    </Box>
  )
}

export default FlagIconDemo
