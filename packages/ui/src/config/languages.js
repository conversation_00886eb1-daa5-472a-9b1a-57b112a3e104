/**
 * Language configuration with SVG flag support
 * Provides consistent flag display across all browsers and operating systems
 */

// Language configuration with multiple flag options
export const languageConfig = [
  {
    code: 'vi',
    name: 'Tiếng Việt',
    englishName: 'Vietnamese',
    nativeName: 'Tiếng Việt',
    flagEmoji: '🇻🇳',
    flagCode: 'vn', // ISO 3166-1 alpha-2 code for CDN
    rtl: false,
    phoneticSupport: false
  },
  {
    code: 'en', 
    name: 'English',
    englishName: 'English',
    nativeName: 'English',
    flagEmoji: '🇺🇸',
    flagCode: 'us', // Using US flag for English
    rtl: false,
    phoneticSupport: true // IPA support
  },
  {
    code: 'ja',
    name: '日本語', 
    englishName: 'Japanese',
    nativeName: '日本語',
    flagEmoji: '🇯🇵',
    flagCode: 'jp',
    rtl: false,
    phoneticSupport: true // Romaji support
  },
  {
    code: 'zh',
    name: '中文',
    englishName: 'Chinese', 
    nativeName: '中文',
    flagEmoji: '🇨🇳',
    flagCode: 'cn',
    rtl: false,
    phoneticSupport: true // Pinyin support
  }
]

// CDN URLs for flag icons
export const flagCdnProviders = {
  flagicons: (countryCode) => `https://flagicons.lipis.dev/flags/4x3/${countryCode}.svg`,
  jsdelivr: (countryCode) => `https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/${countryCode.toUpperCase()}.svg`,
  flagpedia: (countryCode) => `https://flagpedia.net/data/flags/w580/${countryCode}.png`,
  restcountries: (countryCode) => `https://restcountries.com/data/${countryCode}.svg`
}

// Utility functions
export const getLanguageByCode = (code) => {
  return languageConfig.find(lang => lang.code === code)
}

export const getLanguageByEnglishName = (englishName) => {
  return languageConfig.find(lang => 
    lang.englishName.toLowerCase() === englishName.toLowerCase()
  )
}

export const getSupportedLanguageCodes = () => {
  return languageConfig.map(lang => lang.code)
}

export const getLanguagesWithPhoneticSupport = () => {
  return languageConfig.filter(lang => lang.phoneticSupport)
}

export const getFlagUrl = (countryCode, provider = 'flagicons') => {
  const urlGenerator = flagCdnProviders[provider]
  return urlGenerator ? urlGenerator(countryCode) : null
}

// Language mapping for translation API
export const getApiLanguageName = (displayName, t = null) => {
  // Handle language detection case
  if (displayName === 'Phát hiện ngôn ngữ' || 
      displayName === 'Detect Language' ||
      displayName === '言語検出' ||
      displayName === '检测语言') {
    return null // Don't send input_language for language detection
  }

  // Find by display name first
  const langByName = languageConfig.find(lang => lang.name === displayName)
  if (langByName) {
    return langByName.englishName
  }

  // Fallback mapping for legacy support
  const languageMap = {
    // Vietnamese interface
    'Việt': 'Vietnamese',
    'Anh': 'English', 
    'Nhật': 'Japanese',
    'Trung': 'Chinese',
    // English interface
    'Vietnamese': 'Vietnamese',
    'English': 'English',
    'Japanese': 'Japanese', 
    'Chinese': 'Chinese',
    // Japanese interface
    'ベトナム語': 'Vietnamese',
    '英語': 'English',
    '日本語': 'Japanese',
    '中国語': 'Chinese',
    // Chinese interface
    '越南语': 'Vietnamese',
    '英语': 'English', 
    '日语': 'Japanese',
    '中文': 'Chinese'
  }

  return languageMap[displayName] || displayName
}

// Generate language options for UI components
export const generateLanguageOptions = (includeDetectLanguage = false, t = null) => {
  const options = languageConfig.map(lang => ({
    code: lang.code,
    label: lang.name,
    englishName: lang.englishName,
    flagEmoji: lang.flagEmoji,
    flagCode: lang.flagCode
  }))

  if (includeDetectLanguage && t) {
    options.unshift({
      code: 'detect',
      label: t('languages.detectLanguage'),
      englishName: 'Detect Language',
      flagEmoji: '🔍',
      flagCode: null
    })
  }

  return options
}

// Export default configuration for backward compatibility
export default languageConfig
