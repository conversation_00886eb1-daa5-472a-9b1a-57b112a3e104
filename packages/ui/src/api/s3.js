import client from './client'

const listS3Objects = ({ prefix, continuationToken }) => client.get(`/s3/listS3Objects`, { params: { prefix, continuationToken } })
const uploadS3Object = (body) => client.post(`/s3/uploadS3Object`, body)
const getS3Object = (key) => client.get(`/s3/getS3Object/${key}`)
const deleteS3Object = (key) => client.delete(`/s3/deleteS3Object/${key}`)

export { listS3Objects, uploadS3Object, getS3Object, deleteS3Object }
