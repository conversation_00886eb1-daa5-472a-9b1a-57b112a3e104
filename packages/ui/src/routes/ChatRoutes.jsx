import Loadable from '@/ui-component/loading/Loadable'
import Chat<PERSON>rovider from '@/views/Chat/context/ChatContext'
import { lazy } from 'react'
import { Outlet } from 'react-router'

const Chat = Loadable(lazy(() => import('@/views/Chat')))

const ChatRoutes = {
  path: 'chat',
  element: (
    <ChatProvider>
      <Outlet />
    </ChatProvider>
  ),
  children: [
    {
      path: ':chatFlowId/:chatSessionId',
      element: <Chat />
    },
    {
      path: ':chatFlowId',
      element: <Chat />
    },
    {
      path: '',
      element: <Chat />
    }
  ]
}

export default ChatRoutes
