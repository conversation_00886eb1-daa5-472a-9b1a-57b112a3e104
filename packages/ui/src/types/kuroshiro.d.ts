declare module '@patdx/kuromoji' {
  interface LoaderConfig {
    loadArrayBuffer(url: string): Promise<ArrayBufferLike>
  }

  interface TokenizerBuilderOptions {
    loader: LoaderConfig
  }

  interface Token {
    word_id: number
    word_type: 'KNOWN' | 'UNKNOWN'
    word_position: number
    surface_form: string
    pos: string
    pos_detail_1: string
    pos_detail_2: string
    pos_detail_3: string
    conjugated_type: string
    conjugated_form: string
    basic_form: string
    reading: string
    pronunciation: string
  }

  interface Tokenizer {
    tokenize(text: string): Token[]
  }

  class TokenizerBuilder {
    constructor(options: TokenizerBuilderOptions)
    build(): Promise<Tokenizer>
  }

  export { TokenizerBuilder, LoaderConfig, Token, Tokenizer }
}
