/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { Modal } from '@mui/material'
import PropTypes from 'prop-types'
import { useCallback, useState } from 'react'

const ModalPreviewImage = ({ image, open, onClose }) => {
  const [scale, setScale] = useState(1)

  const handleWheel = useCallback(
    (e) => {
      e.preventDefault()
      const delta = e.deltaY * -0.001
      const newScale = Math.min(Math.max(0.1, scale + delta), 2)
      setScale(newScale)
    },
    [scale]
  )

  return (
    <Modal closeAfterTransition open={open} onClose={onClose} className='flex items-center justify-center'>
      <div
        style={{
          height: '100vh',
          width: '100vw',
          overflow: 'hidden',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
        onWheel={handleWheel}
        onClick={onClose}
      >
        <img
          src={image}
          alt='Preview'
          style={{
            transform: `scale(${scale})`,
            transformOrigin: 'center center',
            transition: 'transform 0.1s ease-out'
          }}
          onClick={(e) => {
            e.stopPropagation()
          }}
          className='outline-none'
        />
      </div>
    </Modal>
  )
}

ModalPreviewImage.propTypes = {
  image: PropTypes.string,
  open: PropTypes.bool,
  onClose: PropTypes.func
}

export default ModalPreviewImage
