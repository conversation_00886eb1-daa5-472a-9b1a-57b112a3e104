import { Box } from '@mui/material'
import { styled } from '@mui/material/styles'
import PropTypes from 'prop-types'

const DotWrapper = styled('div')({
  display: 'flex',
  alignItems: 'center',
  gap: '4px'
})

const Dot = styled('div')(({ delay = 0 }) => ({
  width: '8px',
  height: '8px',
  backgroundColor: 'currentColor',
  borderRadius: '50%',
  animation: 'dotBounce 1s infinite',
  animationDelay: `${delay}s`,
  '@keyframes dotBounce': {
    '0%, 100%': {
      transform: 'translateY(0)'
    },
    '50%': {
      transform: 'translateY(-10px)'
    }
  }
}))

const DotLoading = ({ color = 'inherit' }) => {
  return (
    <Box color={color}>
      <DotWrapper>
        <Dot delay={0} />
        <Dot delay={0.2} />
        <Dot delay={0.4} />
      </DotWrapper>
    </Box>
  )
}

DotLoading.propTypes = {
  color: PropTypes.string
}

export default DotLoading
