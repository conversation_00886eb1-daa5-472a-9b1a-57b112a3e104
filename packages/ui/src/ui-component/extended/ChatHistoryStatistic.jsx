import chatflowsApi from '@/api/chatflows'
import { closeSnackbar as closeSnackbarAction, enqueueSnackbar as enqueueSnackbarAction, SET_CHATFLOW } from '@/store/actions'
import { StyledButton } from '@/ui-component/button/StyledButton'
import { SwitchInput } from '@/ui-component/switch/Switch'
import useNotifier from '@/utils/useNotifier'
import { Box, Button } from '@mui/material'
import { IconX } from '@tabler/icons-react'
import PropTypes from 'prop-types'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'

const ChatHistoryStatistic = ({ dialogProps }) => {
  const [showDashboard, setShowDashboard] = useState(false)

  const dispatch = useDispatch()

  useNotifier()

  const enqueueSnackbar = (...args) => dispatch(enqueueSnackbarAction(...args))
  const closeSnackbar = (...args) => dispatch(closeSnackbarAction(...args))

  const handleChange = (value) => {
    setShowDashboard(value)
  }

  const onSave = async () => {
    try {
      const saveResp = await chatflowsApi.updateChatflow(dialogProps.chatflow.id, {
        showDashboard
      })
      if (saveResp.data) {
        enqueueSnackbar({
          message: 'Đã lưu cài đặt hiển thị lịch sử và thống kê.',
          options: {
            key: new Date().getTime() + Math.random(),
            variant: 'success',
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
        dispatch({ type: SET_CHATFLOW, chatflow: saveResp.data })
      }
    } catch (error) {
      enqueueSnackbar({
        message: `Không thể lưu cài đặt hiển thị lịch sử và thống kê: ${
          typeof error.response.data === 'object' ? error.response.data.message : error.response.data
        }`,
        options: {
          key: new Date().getTime() + Math.random(),
          variant: 'error',
          persist: true,
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
    }
  }

  useEffect(() => {
    if (dialogProps?.chatflow?.showDashboard) setShowDashboard(dialogProps?.chatflow?.showDashboard || false)
  }, [dialogProps])

  return (
    <>
      <Box sx={{ width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <SwitchInput label='Cho phép hiển thị lịch sử và thống kê' onChange={handleChange} value={showDashboard} />
      </Box>
      <StyledButton style={{ marginBottom: 10, marginTop: 10 }} variant='contained' onClick={onSave}>
        Lưu
      </StyledButton>
    </>
  )
}

ChatHistoryStatistic.propTypes = {
  dialogProps: PropTypes.any
}

export default ChatHistoryStatistic
