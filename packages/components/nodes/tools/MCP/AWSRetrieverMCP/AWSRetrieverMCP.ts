import { Tool } from '@langchain/core/tools'
import {
  getCredentialData,
  getCredentialParam,
  getNodeModulesPackagePath,
  ICommonObject,
  INode,
  INodeData,
  INodeOptionsValue,
  INodeParams
} from '../../../../src'
import { MCPToolkit } from '../core'

class AWSRetriever_MCP implements INode {
  label: string
  name: string
  version: number
  description: string
  type: string
  icon: string
  category: string
  baseClasses: string[]
  documentation: string
  credential: INodeParams
  inputs: INodeParams[]

  constructor() {
    this.label = 'AWS Retriever MCP'
    this.name = 'awsRetrieverMCP'
    this.version = 1.0
    this.type = 'AWS Retriever MCP Tool'
    this.icon = 'aws.svg'
    this.category = 'MCP'
    this.description = 'MCP server that integrates with AWS Retrieval services for document and information retrieval'
    this.documentation = ''
    this.credential = {
      label: 'Connect Credential',
      name: 'credential',
      type: 'credential',
      credentialNames: ['awsApi']
    }
    this.inputs = [
      {
        label: 'Available Actions',
        name: 'mcpActions',
        type: 'asyncMultiOptions',
        loadMethod: 'listActions',
        refresh: true
      },
      {
        label: 'Region',
        name: 'region',
        type: 'asyncOptions',
        loadMethod: 'listRegions',
        default: 'us-east-1',
        additionalParams: true
      }
    ]
    this.baseClasses = ['Tool']
  }

  //@ts-ignore
  loadMethods = {
    listActions: async (nodeData: INodeData, options: ICommonObject): Promise<INodeOptionsValue[]> => {
      try {
        const toolset = await this.getTools(nodeData, options)
        toolset.sort((a: any, b: any) => a.name.localeCompare(b.name))

        return toolset.map(({ name, ...rest }) => ({
          label: name.toUpperCase(),
          name: name,
          description: rest.description || name
        }))
      } catch (error) {
        return [
          {
            label: 'No Available Actions',
            name: 'error',
            description: 'No available actions, please check your API key and refresh'
          }
        ]
      }
    }
  }

  async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
    const tools = await this.getTools(nodeData, options)

    const _mcpActions = nodeData.inputs?.mcpActions
    let mcpActions = []
    if (_mcpActions) {
      try {
        mcpActions = typeof _mcpActions === 'string' ? JSON.parse(_mcpActions) : _mcpActions
      } catch (error) {
        console.error('Error parsing mcp actions:', error)
      }
    }

    return tools.filter((tool: any) => mcpActions.includes(tool.name))
  }

  async getTools(nodeData: INodeData, options: ICommonObject): Promise<Tool[]> {
    const credentialData = await getCredentialData(nodeData.credential ?? '', options)
    const credentialApiKey = getCredentialParam('awsKey', credentialData, nodeData)
    const credentialApiSecret = getCredentialParam('awsSecret', credentialData, nodeData)
    const packagePath = getNodeModulesPackagePath('@modelcontextprotocol/server-aws-kb-retrieval/dist/index.js')

    const serverParams = {
      command: 'node',
      args: [packagePath],
      env: {
        AWS_ACCESS_KEY_ID: credentialApiKey,
        AWS_SECRET_ACCESS_KEY: credentialApiSecret,
        AWS_REGION: (nodeData.inputs?.region as string) || 'us-east-1'
      }
    }

    const toolkit = new MCPToolkit(serverParams, 'stdio')
    await toolkit.initialize()

    const tools = toolkit.tools ?? []

    return tools as Tool[]
  }
}

module.exports = { nodeClass: AWSRetriever_MCP }
