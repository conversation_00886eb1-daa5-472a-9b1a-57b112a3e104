import { Tool } from '@langchain/core/tools'
import { ICommonObject, INode, INodeData, INodeOptionsValue, INodeParams } from '../../../../src/Interface'
import { MCPToolkit } from '../core'
import zodToJsonSchema from 'zod-to-json-schema'

const mcpServerConfig = `{
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"]
}`

class Custom_MCP implements INode {
  label: string
  name: string
  version: number
  description: string
  type: string
  icon: string
  category: string
  baseClasses: string[]
  documentation: string
  credential: INodeParams
  inputs: INodeParams[]

  constructor() {
    this.label = 'MCP Client'
    this.name = 'customMCP'
    this.version = 1.0
    this.type = 'MCP Tool'
    this.icon = 'customMCP.png'
    this.category = 'MCP'
    this.description = 'MCP Client'
    this.documentation = 'https://modelcontextprotocol.io/introduction'
    this.inputs = [
      {
        label: 'MCP Server',
        name: 'baseUrl',
        placeholder: 'MCP Server',
        type: 'string',
        optional: true
      },
      {
        label: 'Available Actions',
        name: 'mcpActions',
        type: 'asyncMultiOptions',
        loadMethod: 'listActions',
        refresh: true
      },
      {
        label: 'Server Environment Variables',
        name: 'mcpServerEnv',
        type: 'code',
        hideCodeExecute: true,
        placeholder: 'API_KEY=your-api-key\nTIMEZONE=Asia/Ho_Chi_Minh',
        additionalParams: true,
        optional: true,
        description: 'Set environment variables for the MCP server (one per line)'
      },
      {
        label: 'MCP Server Config',
        name: 'mcpServerConfig',
        type: 'code',
        hideCodeExecute: true,
        placeholder: mcpServerConfig,
        additionalParams: true,
        optional: true
      }
    ]
    this.baseClasses = ['Tool']
  }

  //@ts-ignore
  loadMethods = {
    listActions: async (nodeData: INodeData): Promise<INodeOptionsValue[]> => {
      try {
        const toolset = await this.getTools(nodeData)
        toolset.sort((a: any, b: any) => a.name.localeCompare(b.name))

        return toolset.map(({ name, ...rest }) => {
          return {
            label: name.toUpperCase(),
            name: name,
            description: rest.description || name,
            schema: zodToJsonSchema(rest.schema)
          }
        })
      } catch (error) {
        return [
          {
            label: 'No Available Actions',
            name: 'error',
            description: 'No available actions, please check your API key and refresh'
          }
        ]
      }
    }
  }

  async init(nodeData: INodeData, _input: string, options: ICommonObject): Promise<any> {
    const { userId, groupID: groupId } = options
    const tools = await this.getTools(nodeData, { userId, groupId })

    const _mcpActions = nodeData.inputs?.mcpActions
    let mcpActions = []
    if (_mcpActions) {
      try {
        mcpActions = typeof _mcpActions === 'string' ? JSON.parse(_mcpActions) : _mcpActions
      } catch (error) {
        console.error('Error parsing mcp actions:', error)
      }
    }

    return tools.filter((tool: any) => mcpActions.includes(tool.name))
  }

  async getTools(nodeData: INodeData, options?: ICommonObject): Promise<Tool[]> {
    let mcpServerConfig = nodeData.inputs?.mcpServerConfig as string
    let baseUrl: string | undefined = nodeData.inputs?.baseUrl

    if (!mcpServerConfig && !baseUrl) {
      baseUrl = process.env.DEFAULT_MCP_SERVER_URL || `http://localhost:${+(process.env.PORT || 3000) + 1}/mcp`
      const separator = baseUrl.includes('?') ? '&' : '?'
      baseUrl += `${separator}userId=${options?.userId}&groupId=${options?.groupId}`
    }

    if (baseUrl) {
      let mcpConfigObj = {}

      try {
        mcpConfigObj = JSON.parse(mcpServerConfig)
      } catch {
        // ignored
      }

      mcpServerConfig = JSON.stringify({
        ...mcpConfigObj,
        url: baseUrl
      })
    }

    if (!mcpServerConfig) {
      throw new Error('MCP Server Config is required')
    }

    try {
      let serverParams
      if (typeof mcpServerConfig === 'object') {
        serverParams = mcpServerConfig
      } else if (typeof mcpServerConfig === 'string') {
        const serverParamsString = convertToValidJSONString(mcpServerConfig)
        serverParams = JSON.parse(serverParamsString)
      }

      const mcpServerEnv = nodeData.inputs?.mcpServerEnv as string | undefined

      let parsedEnv: { [key: string]: string } = {}
      if (mcpServerEnv) {
        const lines = mcpServerEnv.split('\n')
        for (const line of lines) {
          const trimmedLine = line.trim()
          if (trimmedLine && !trimmedLine.startsWith('#')) {
            const [key, ...valueParts] = trimmedLine.split('=')
            const value = valueParts.join('=').trim()
            if (key && value) {
              parsedEnv[key.trim()] = value
            }
          }
        }
      }

      const toolkit = new MCPToolkit(
        {
          ...serverParams,
          env: {
            ...serverParams.env,
            ...parsedEnv
          }
        },
        baseUrl ? 'sse' : 'stdio'
      )
      await toolkit.initialize()

      const tools = toolkit.tools ?? []

      return tools as Tool[]
    } catch (error) {
      throw new Error(`Invalid MCP Server Config: ${error}`)
    }
  }
}

function convertToValidJSONString(inputString: string) {
  try {
    const jsObject = Function('return ' + inputString)()
    return JSON.stringify(jsObject, null, 2)
  } catch (error) {
    console.error('Error converting to JSON:', error)
    return ''
  }
}

module.exports = { nodeClass: Custom_MCP }
