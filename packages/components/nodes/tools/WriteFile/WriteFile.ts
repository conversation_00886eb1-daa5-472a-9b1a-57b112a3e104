import { z } from 'zod'
import { StructuredTool, ToolParams } from '@langchain/core/tools'
import { Serializable } from '@langchain/core/load/serializable'
import { NodeFileStore } from 'langchain/stores/file/node'
import { INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses } from '../../../src/utils'
import { PutObjectCommand, S3Client, type _Object } from '@aws-sdk/client-s3'

abstract class BaseFileStore extends Serializable {
  abstract readFile(path: string): Promise<string>
  abstract writeFile(path: string, contents: string): Promise<void>
}

export const BUCKET_NAME = process.env.S3_STORAGE_BUCKET_NAME!

export const s3Client = new S3Client({
  credentials: {
    accessKeyId: process.env.S3_STORAGE_ACCESS_KEY_ID!,
    secretAccessKey: process.env.S3_STORAGE_SECRET_ACCESS_KEY!
  },
  region: process.env.S3_STORAGE_REGION!
})

class WriteFile_Tools implements INode {
  label: string
  name: string
  version: number
  description: string
  type: string
  icon: string
  category: string
  baseClasses: string[]
  inputs: INodeParams[]

  constructor() {
    this.label = 'Write File'
    this.name = 'writeFile'
    this.version = 1.0
    this.type = 'WriteFile'
    this.icon = 'writefile.svg'
    this.category = 'Tools'
    this.description = 'Write file to disk'
    this.baseClasses = [this.type, 'Tool', ...getBaseClasses(WriteFileTool)]
    this.inputs = [
      {
        label: 'Base Path',
        name: 'basePath',
        placeholder: 'Path trên s3 nếu không dùng file_path tự gen',
        type: 'string',
        optional: true
      }
    ]
  }

  async init(nodeData: INodeData): Promise<any> {
    const basePath = nodeData.inputs?.basePath as string
    const store = basePath ? new NodeFileStore(basePath) : new NodeFileStore()
    return new WriteFileTool({ store, basePath })
  }
}

interface WriteFileParams extends ToolParams {
  store: BaseFileStore
  basePath?: string
}

/**
 * Class for writing data to files on the disk. Extends the StructuredTool
 * class.
 */
export class WriteFileTool extends StructuredTool {
  static lc_name() {
    return 'WriteFileTool'
  }

  schema = z.object({
    file_path: z.string().describe('name of file'),
    text: z.string().describe('text to write to file')
  }) as any

  name = 'write_file'

  description = 'Write file from disk'

  store: BaseFileStore
  basePath: string

  constructor({ store, basePath, ...rest }: WriteFileParams) {
    super(rest)
    this.basePath = basePath || ''
    this.store = store
  }

  async _call({ file_path, text }: z.infer<typeof this.schema>) {
    const fileContent = Buffer.from(text, 'utf-8')
    const uploadParams = {
      Bucket: BUCKET_NAME,
      Key: this.basePath || file_path,
      Body: fileContent
    }

    await s3Client.send(new PutObjectCommand(uploadParams))

    // await this.store.writeFile(file_path, text)
    return 'File written to successfully.'
  }
}

module.exports = { nodeClass: WriteFile_Tools }
